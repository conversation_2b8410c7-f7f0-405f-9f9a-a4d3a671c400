import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {getIsLikeRating, getLikesRatingCourse} from '../../modules/rate/da';
import {DataController} from '../../base/baseController';
import {randomGID} from '../../utils/Utils';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
import store from '../store/store';
export const FETCH_COURSE_RATINGS = 'FETCH_COURSE_RATINGS_REQUEST';
export const ADD_RATING = 'ADD_RATING_REQUEST';
export const UPDATE_LIKE = 'UPDATE_LIKE';
export const ADD_COMMENT = 'ADD_COMMENT';
export const RESET_COURSE_RATINGS = 'RESET_COURSE_RATINGS';
const initialState: {
  courseRatings: {
    [courseId: string]: {
      list: any[];
      listP: any[];
      averageRating: number;
      totalCount: number;
    };
  };
  data: any[];
  loading: boolean;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  courseRatings: {},
  data: [],
  loading: true,
  success: false,
  error: null,
};
export const ratingSlice = createSlice({
  name: 'rating',
  initialState: initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case FETCH_COURSE_RATINGS:
          const courseId = action.payload.courseId;
          state.courseRatings[courseId] = {
            list: action.payload.list,
            listP: action.payload.listP,
            averageRating: action.payload.Average,
            totalCount: action.payload.totalCount ?? 0,
          };
          break;
        case ADD_RATING:
          const addCourseId = action.payload.courseId;
          if (state.courseRatings[addCourseId]) {
            const currentRatings = state.courseRatings[addCourseId];
            const updatedList = [action.payload.data,
              ...currentRatings.list,
            ];
            const updatedListP = [action.payload.data,
              ...currentRatings.listP,
            ];
            const newTotalCount = currentRatings.totalCount + 1;
            const newAverage =
              newTotalCount > 0
                ? updatedList.reduce((sum, item) => sum + item.Value, 0) /
                  newTotalCount
                : 0;
            state.courseRatings[addCourseId] = {
              list: updatedList,
              listP: updatedListP,
              averageRating: newAverage,
              totalCount: newTotalCount,
            };
          }
          break;
        case UPDATE_LIKE:
          const likeCourseId = action.payload.courseId;
          if (state.courseRatings[likeCourseId]) {
            state.courseRatings[likeCourseId].list = [
              ...state.courseRatings[likeCourseId].list.map((rating: any) => {
                if (rating.Id === action.payload.Id) {
                  // Cập nhật thông tin likes
                  return {
                    ...rating,
                    Likes:
                      action.payload.IsLike === true
                        ? (rating.Likes ?? 0) + 1
                        : (rating.Likes ?? 0) - 1,
                    IsLike: action.payload.IsLike,
                  };
                }
                return rating;
              }),
            ];
          }
          break;
        case ADD_COMMENT:
          const commentCourseId = action.payload.courseId;
          if (state.courseRatings[commentCourseId]) {
            const ListComment = [
              ...state.courseRatings[commentCourseId].list,
              action.payload.data,
            ];
            state.courseRatings[commentCourseId].list = ListComment;
          }
          break;
        case RESET_COURSE_RATINGS:
          const resetCourseId = action.payload.courseId;
          if (resetCourseId) {
            delete state.courseRatings[resetCourseId];
          } else {
            // Reset tất cả nếu không có courseId cụ thể
            state.courseRatings = {};
          }
          break;
        default:
          break;
      }
      state.loading = false;
    },
    onFetching: state => {
      state.loading = true;
    },
    // onReset: (state) => {

    // }
  },
});
export default ratingSlice.reducer;
const {handleActions, onFetching} = ratingSlice.actions;
export class RatingActions {
  static getRatingCourse = (id: string) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const courseController = new DataController('Rating');
    const courseResult = await courseController.getPatternList({
      query: `@CourseId: {${id}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Img'],
      },
    });
    if (courseResult.code === 200) {
      var lstId = courseResult.data.map((item: any) => item.Id) ;
      const likes = await getLikesRatingCourse(lstId);
      const islikes = await getIsLikeRating(lstId);
      const ratingPromises = courseResult.data.map(async (item: any) => {
        // Tạo copy của item để tránh mutate dữ liệu gốc
        const itemCopy = {...item};
        try {
          // Lấy dữ liệu customer
          const customer = courseResult.Customer.find(
            (cus: any) => cus.Id === item.CustomerId,
          );
          // Lấy số lượt thích
          const like = likes.find((l: any) => l.RatingId === item.Id);

          // Đếm số lượng comment cho rating này
          const commentCount =
            courseResult.data.filter((t: any) => t.ParentId === item.Id)
              .length || 0;

          if (customer) {
            return {
              ...itemCopy,
              Name: null,
              Content: itemCopy.Message,
              Likes: like?.totalLikes ?? 0,
              IsLike: islikes?.some((l: any) => l.RatingId === item.Id),
              Comment: commentCount,
              relativeUser: {
                image: customer.Img,
                title: `${customer.Name}`,
                subtitle: itemCopy.DateCreated,
              },
            };
          } else {
            return {
              ...itemCopy,
              Likes: like?.totalLikes ?? 0,
              Comment: commentCount,
              IsLike: islikes,
            };
          }
        } catch (error) {
          console.error('Error processing rating item:', error);
          // Trả về bản copy với thông tin lỗi
          return {
            ...itemCopy,
            Likes: 0,
            Comment: 0,
            hasError: true,
          };
        }
      });
      const lst2 = await Promise.all(ratingPromises);
      var list = lst2.filter((item: any) => item.ParentId == null);
      const Average =
        list.length > 0
          ? lst2.reduce((sum: number, item: any) => sum + item.Value, 0) /
            list.length
          : 0;

      dispatch(
        handleActions({
          type: FETCH_COURSE_RATINGS,
          courseId: id,
          list: lst2,
          listP: list,
          Average: Average,
          totalCount: list.length,
        }),
      );
    }
  };
  static addRating = (data: any) => async (dispatch: Dispatch) => {
    const courseController = new DataController('Rating');
    const courseResult = await courseController.add([data]);
    debugger
    if (courseResult.code === 200) {
      const dataMap = {
        ...data,
        Likes: 0,
        Content: data.Message,
        IsLike: false,
        Comment: 0,
        relativeUser: {
          image: store.getState().customer.data.avatarUrl,
          title: `${store.getState().customer.data.Name}`,
          subtitle: new Date().getTime(),
        },
      };
      dispatch(
        handleActions({
          type: ADD_RATING,
          courseId: data.CourseId,
          data: dataMap,
        }),
      );
    }
  };
  static updateLike =
    (id: string, isUnLike: boolean, courseId: string) => async (dispatch: Dispatch) => {
      const likeController = new DataController('Like_Rating');
      var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

      if (cusId) {
        if (isUnLike === true) {
          const result = await likeController.getListSimple({
            query: `@CustomerId: {${cusId}} @RatingId:{${id}}`,
          });
          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(
                handleActions({
                  type: UPDATE_LIKE,
                  courseId: courseId,
                  Id: id,
                  IsLike: false,
                }),
              );
            }
          }
        } else {
          const data = {
            Id: randomGID(),
            CustomerId: cusId,
            RatingId: id,
            DateCreated: new Date().getTime(),
          };
          const Result = await likeController.add([data]);
          if (Result.code === 200) {
            dispatch(
              handleActions({
                type: UPDATE_LIKE,
                courseId: courseId,
                Id: id,
                IsLike: true,
              }),
            );
          }
        }
      }
    };
  static addCommentRating =
    (id: string, content: string, courseId: string) =>
    async (dispatch: Dispatch) => {
      const courseController = new DataController('Rating');
      var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
      if (cusId) {
        const customer = store.getState().customer.data;
        const data = {
          Id: randomGID(),
          ParentId: id,
          CustomerId: cusId,
          CourseId: courseId,
          Message: content,
          DateCreated: new Date().getTime(),
        };
        const courseResult = await courseController.add([data]);
        if (courseResult.code === 200) {
          const dataMap = {
            ...data,
            Likes: 0,
            Content: data.Message,
            IsLike: false,
            Comment: 0,
            relativeUser: {
              image: customer.data.Img,
              title: `${customer.data.Name}`,
              DateCreated: new Date().getTime(),
            },
          };
          dispatch(
            handleActions({
              type: ADD_COMMENT,
              courseId: courseId,
              data: dataMap,
            }),
          );
        }
      }
    };

  static resetCourseRatings = (courseId?: string) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: RESET_COURSE_RATINGS,
        courseId: courseId,
      }),
    );
  };
}
