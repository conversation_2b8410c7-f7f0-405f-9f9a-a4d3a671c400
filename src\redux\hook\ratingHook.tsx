import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import { useEffect } from 'react';
import { RatingActions } from '../reducers/ratingReducer';

export function useRatingData(courseId: string) {
    const dispatch: AppDispatch = useDispatch();
    const allRatings = useSelector((state: RootState) => state.ratings.courseRatings);
    const loading = useSelector((state: RootState) => state.ratings.loading);
    const error = useSelector((state: RootState) => state.ratings.error);
    // Lấy rating cho courseId cụ thể, nếu không có thì trả về default
    const ratings = allRatings[courseId] || {
        list: [],
        listP: [],
        averageRating: 0,
        totalCount: 0,
    };

    useEffect(() => {
        // Chỉ load dữ liệu nếu chưa có dữ liệu cho courseId này
        if (!allRatings[courseId]) {
            dispatch(RatingActions.getRatingCourse(courseId));
        }
    }, [dispatch, courseId, allRatings]);

    return { ratings, loading, error };
}
