import React, {useRef, useState, useEffect, useMemo, useCallback} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  TouchableWithoutFeedback,
  TouchableOpacity,
  View,
  Dimensions,
  Animated,
  Alert,
  PixelRatio,
  Platform,
} from 'react-native';
import {Text} from 'react-native-paper';
import {Winicon} from 'wini-mobile-components';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import store, {AppDispatch, RootState} from '../../redux/store/store';
import {
  gameAction,
  setCurrentMilestoneId,
} from './ailatrieuphu/redux/gameSlice';
import {GameStatus} from '../../Config/Contanst';
import {
  useGenericSVGPath,
  useGenericPathTrail,
} from './hooks/useGenericSVGPath';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ultis} from '../../utils/Utils';
import FastImage from '@d11/react-native-fast-image';
import {LoadingUI} from '../../features/loading';
import ChooseLevelModal from './components/ChooseLevelModal';
import HelpModal from './components/HelpModal';
import {GameDA} from './gameDA';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {getGameConfig, GameConfig} from './config/GameConfig';
import StartGameModal from './ailatrieuphu/components/StartGameModal';
import StaticEnum from '../../Config/StaticEnum';
import {BackHandler} from 'react-native';
import {useAudioManager} from './hooks/useAudioManager';
import {navigateBack} from '../../router/router';

// Responsive utilities
const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const scale = SCREEN_WIDTH / 375;

export function normalize(size: number): number {
  const newSize = size * scale;
  if (Platform.OS === 'ios') {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  }
  return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
}

export function wp(percentage: number): number {
  return (percentage * SCREEN_WIDTH) / 100;
}

export function hp(percentage: number): number {
  return (percentage * SCREEN_HEIGHT) / 100;
}

// Calculate scale for milestones based on bottom position
// Cọc gỗ số 1 bắt đầu từ bottom (top cao), cọc gỗ cuối ở top (top thấp)
// Cọc gỗ càng gần bottom (bottom nhỏ) thì scale càng lớn (gần người chơi)
// Cọc gỗ càng xa bottom (bottom lớn) thì scale càng nhỏ (xa người chơi)
const calculateScale = (percentageY: number, milestonePositions?: any[]) => {
  const bottom = 1 - percentageY; // Chuyển từ top sang bottom

  // Tự động tính range dựa trên milestone positions nếu có
  let minBottom = 0.25; // Default: cọc gỗ gần nhất
  let maxBottom = 0.58; // Default: cọc gỗ xa nhất

  if (milestonePositions && milestonePositions.length > 0) {
    const topValues = milestonePositions.map(m => m.percentageY || m.top || 0.5);
    const minTop = Math.min(...topValues);
    const maxTop = Math.max(...topValues);
    minBottom = 1 - maxTop; // Cọc có top cao nhất = bottom nhỏ nhất = gần nhất
    maxBottom = 1 - minTop; // Cọc có top thấp nhất = bottom lớn nhất = xa nhất
  }

  const baseScale = SCREEN_WIDTH / 375;
  const minScale = 0.6 * baseScale; // Scale nhỏ nhất cho cọc xa
  const maxScale = 1.1 * baseScale; // Scale lớn nhất cho cọc gần

  // Normalize bottom position (0 = gần nhất, 1 = xa nhất)
  const normalized = Math.max(0, Math.min(1, (bottom - minBottom) / (maxBottom - minBottom)));
  // Đảo ngược vì bottom lớn = xa = scale nhỏ
  return maxScale - normalized * (maxScale - minScale);
};

// Generic Milestone Component Props
interface GenericMilestoneProps {
  status: 'completed' | 'in-progress' | 'locked';
  number: number;
  levelName?: string;
  onPress: () => void;
  scaleFactor: number;
  gameConfig: GameConfig;
}

// Generic Milestone Component
const GenericMilestoneComponent: React.FC<GenericMilestoneProps> = ({
  status,
  number,
  onPress,
  scaleFactor,
  gameConfig,
}) => {
  const scaleAnim = useRef(new Animated.Value(scaleFactor)).current;
  const [isAnimating, setIsAnimating] = useState(false);

  const getMilestoneImage = (milestoneStatus: string) => {
    switch (milestoneStatus) {
      case 'completed':
        return gameConfig.milestoneImages.completed;
      case 'in-progress':
        return gameConfig.milestoneImages.inProgress;
      default:
        return gameConfig.milestoneImages.locked;
    }
  };

  const handlePress = () => {
    if (isAnimating) {
      return;
    }

    setIsAnimating(true);
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: scaleFactor * 1.2,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: scaleFactor,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsAnimating(false);
      onPress();
    });
  };

  return (
    <View style={{position: 'relative'}}>
      <TouchableWithoutFeedback onPress={handlePress}>
        <Animated.View
          style={[styles.milestoneContent, {transform: [{scale: scaleAnim}]}]}>
          <FastImage
            source={getMilestoneImage(status)}
            style={styles.milestoneImage}
            resizeMode={FastImage.resizeMode.contain}
          />
          <Text
            style={[
              styles.milestoneNumber,
              {color: gameConfig.colors.primary},
            ]}>
            {number}
          </Text>
        </Animated.View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const GenericGameHomeScreen = () => {
  const dispatch: AppDispatch = useDispatch();
  const gameState = useSelector((state: RootState) => state.game);
  const gameDa = new GameDA();
  const customer = useSelectorCustomerState().data;
  //router param
  const route = useRoute<any>();
  const {gameId} = route.params || {gameId: 'ALTP'};

  // Get game configuration
  const gameConfig = useMemo(() => {
    try {
      return getGameConfig(gameId);
    } catch (error) {
      console.error('Error getting game config:', error);
      return getGameConfig('ALTP'); // Fallback to ALTP
    }
  }, [gameId]);

  const [orientation, setOrientation] = useState({
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  });

  // State management
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMilestone, setSelectedMilestone] = useState<{
    number: number;
    levelName?: string;
  } | null>(null);
  const [showLevelModal, setShowLevelModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState('N5');
  const [unlockedLevels, setUnlockedLevels] = useState<string[]>([]);
  const [selectedCompetenceId, setSelectedCompetenceId] = useState<number>(0);
  const [apiStages, setApiStages] = useState<any[]>([]);
  const [loadingStages, setLoadingStages] = useState(false);
  const [score, setScore] = useState<number>(0);

  // Audio Manager setup
  const {
    audioState,
    initializeAudio,
    playAudio,
    pauseAudio,
    resumeAudio,
    stopAudio,
    clearAudio,
    forceCleanup,
  } = useAudioManager();

  // Use refs to store audio functions to avoid dependency issues
  const audioFunctionsRef = useRef({
    initializeAudio,
    playAudio,
    resumeAudio,
    stopAudio,
    clearAudio,
  });

  // Update refs when functions change
  useEffect(() => {
    audioFunctionsRef.current = {
      initializeAudio,
      playAudio,
      resumeAudio,
      stopAudio,
      clearAudio,
    };
  }, [initializeAudio, playAudio, resumeAudio, stopAudio, clearAudio]);

  // Generic SVG Path hooks
  const {milestonePositions: svgMilestonePositions} = useGenericSVGPath(
    gameId,
    {
      width: orientation.width,
      height: orientation.height,
    },
  );

  // Path trail for completed milestones
  const completedMilestoneIds =
    gameState.Milestone?.filter(m => m.status === GameStatus.Completed).map(
      m => m.id,
    ) || [];

  const {completedTrail} = useGenericPathTrail(gameId, completedMilestoneIds, {
    width: orientation.width,
    height: orientation.height,
  });

  // Load milestones when component mounts and when screen is focused
  useEffect(() => {
    dispatch(gameAction.getMilestones(gameId));
  }, [dispatch, gameId]);
  // Cleanup on unmount only
  useEffect(() => {
    return () => {
      console.log('Component unmounting - force cleanup');
      forceCleanup();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Store audioState and gameConfig in ref to avoid dependency issues
  const audioStateRef = useRef(audioState);
  const gameConfigRef = useRef(gameConfig);

  useEffect(() => {
    audioStateRef.current = audioState;
    gameConfigRef.current = gameConfig;
  }, [audioState, gameConfig]);

  // Simple focus effect without complex dependencies
  useFocusEffect(
    useCallback(() => {
      console.log('GenericGameHomeScreen focused - starting audio');

      // Simple approach: always try to start audio on focus
      const startAudio = async () => {
        try {
          const audioFunctions = audioFunctionsRef.current;
          await audioFunctions.initializeAudio(
            gameConfigRef.current.audio || 'music_bg_chung.mp3',
          );
          await audioFunctions.playAudio(true);
        } catch (error) {
          console.error('Failed to start audio on focus:', error);
        }
      };

      startAudio();

      return () => {
        console.log('GenericGameHomeScreen blurred - stopping audio');
        // Always stop audio on blur
        const audioFunctions = audioFunctionsRef.current;
        audioFunctions.stopAudio();
      };
    }, []), // Empty dependency array
  );

  // Handle hardware back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        console.log('Hardware back button pressed');
        audioFunctionsRef.current.clearAudio();
        return false; // Let default behavior handle navigation
      },
    );

    return () => backHandler.remove();
  }, []); // No dependencies needed since we use ref

  // Debug: Log milestone status changes (can be removed in production)
  useEffect(() => {
    if (gameState.Milestone && gameState.Milestone.length > 0) {
      console.log(
        'Milestone status updated:',
        gameState.Milestone.map(m => ({
          id: m.id,
          status: m.status,
          statusName:
            m.status === GameStatus.Completed
              ? 'completed'
              : m.status === GameStatus.Pending
              ? 'in-progress'
              : 'locked',
        })),
      );
    }
  }, [gameState.Milestone]);

  // Handle orientation changes
  useEffect(() => {
    const updateLayout = () => {
      const {width, height} = Dimensions.get('window');
      if (width !== orientation.width || height !== orientation.height) {
        setOrientation({width, height});
      }
    };

    const dimensionsHandler = Dimensions.addEventListener(
      'change',
      updateLayout,
    );
    return () => dimensionsHandler.remove();
  }, [orientation.width, orientation.height]);

  // Load stages by level
  const loadStagesByLevel = async (levelId: number) => {
    if (levelId === 0 || !levelId) {
      return;
    }
    try {
      setLoadingStages(true);
      setSelectedCompetenceId(levelId);
      const stages = StaticEnum.GameStagebyCompetency.find(
        (comp: any) => comp.competenceId === levelId && comp.gameId === gameId,
      );
      if (stages && stages.data && stages.data.length > 0) {
        setApiStages(stages.data);
      } else {
        setApiStages([]);
      }
    } catch (error) {
      setApiStages([]);
    } finally {
      setLoadingStages(false);
    }
  };

  // Fetch competence data
  useEffect(() => {
    const fetchCompetence = async () => {
      try {
        const customerCompetence =
          await gameDa.getCompetencebyGameIdAndCustomerId(gameId, customer?.Id);

        if (customerCompetence && customerCompetence.data.length > 0) {
          const currentCompetence = customerCompetence.data.find(
            (comp: any) => comp.Status === 2,
          );

          if (currentCompetence) {
            setSelectedLevel(currentCompetence.Name);
            setUnlockedLevels(customerCompetence.data.map((c: any) => c.Name));
            await loadStagesByLevel(currentCompetence.Competency);
            return;
          }
          const currentLevel = customerCompetence.data[0].Name;
          setSelectedLevel(currentLevel);
          setUnlockedLevels(customerCompetence.data.map((c: any) => c.Name));
          await loadStagesByLevel(customerCompetence.data[0].Competency);
        } else {
          const competence = StaticEnum.GameCompetencybyGame.find(
            (comp: any) => comp.gameId === gameId,
          );
          if (competence && competence.data && competence.data.length > 0) {
            //sort
            competence.data.sort((a: any, b: any) => a.sort - b.sort);
            setSelectedLevel(competence.data[0].name);
            setUnlockedLevels([competence.data[0].name]);
            await loadStagesByLevel(competence.data[0].id);
          }
        }
      } catch (error) {
        console.error('Error fetching competence:', error);
        await loadStagesByLevel(0);
      }
    };

    if (customer?.Id) {
      fetchCompetence();
    }
  }, [customer?.Id, gameId]);

  // Convert milestone positions from API stages using GAME_PATH_CONFIGS
  const milestones = useMemo(() => {
    if (gameState.loading || loadingStages) {
      return [];
    }

    const stagesToShow = apiStages.length > 0 ? apiStages : [];

    if (stagesToShow.length === 0) {
      if (!gameState.Milestone || gameState.Milestone.length === 0) {
        return svgMilestonePositions.map(pos => ({
          ...pos,
          status: pos.id === 1 ? ('in-progress' as const) : ('locked' as const),
        }));
      }

      return svgMilestonePositions.map(pos => {
        const milestone = gameState.Milestone.find(m => m.id === pos.id);
        return {
          ...pos,
          status:
            milestone?.status === GameStatus.Completed
              ? ('completed' as const)
              : milestone?.status === GameStatus.Pending
              ? ('in-progress' as const)
              : ('locked' as const),
        };
      });
    }

    // Create milestones from API stages using SVG path positions
    const maxStages = Math.min(
      stagesToShow.length,
      svgMilestonePositions.length,
    );
    const result = [];

    for (let i = 0; i < maxStages; i++) {
      const stage = stagesToShow[i];
      const position = svgMilestonePositions[i];
      const milestone = gameState.Milestone.find(m => m.id === i + 1);

      result.push({
        id: i + 1,
        percentageX: position.percentageX,
        percentageY: position.percentageY,
        x: position.x,
        y: position.y,
        name:
          stage.Name || stage.Description || position.name || `Chặng ${i + 1}`,
        status:
          milestone?.status === GameStatus.Completed
            ? ('completed' as const)
            : milestone?.status === GameStatus.Pending
            ? ('in-progress' as const)
            : ('locked' as const),
      });
    }

    return result;
  }, [
    gameState.Milestone,
    gameState.loading,
    apiStages,
    loadingStages,
    svgMilestonePositions,
  ]);
  useFocusEffect(
    useCallback(() => {
      fetchScore();
    }, []),
  );
  //user focus

  const fetchScore = async () => {
    try {
      const gameDa = new GameDA();
      const result = await gameDa.getScoreByCustomerIdAndGameId(
        store.getState().customer.data.Id,
        gameId,
      );
      setScore(result ?? 0);
    } catch (error) {
      console.error('Lỗi khi lấy thông tin điểm:', error);
    }
  };

  // Handle milestone press
  const handleMilestonePress = useMemo(() => {
    return (status: string, number: number, levelName?: string) => {
      if (status === 'locked') {
        Alert.alert(
          `Chặng ${number}: ${levelName}`,
          'Bạn cần hoàn thành các chặng trước để mở khóa chặng này.',
        );
      } else {
        setSelectedMilestone({number, levelName});
        dispatch(setCurrentMilestoneId(number));
        setModalVisible(true);
      }
    };
  }, [dispatch]);

  const closeModal = () => setModalVisible(false);

  const handleStartGame = () => {
    // Stop and clear background music when starting game
    audioFunctionsRef.current.clearAudio();
  };
  const showLevelSelection = () => setShowLevelModal(true);
  const showHelp = () => setShowHelpModal(true);

  const handleSelectLevel = async (level: any) => {
    setSelectedLevel(level.Name);
    await loadStagesByLevel(level.Id);
  };

  return (
    <View style={styles.mainContainer}>
      <ImageBackground
        source={gameConfig.backgroundImage}
        style={styles.backgroundImage}
        resizeMode="stretch">
        <SafeAreaView style={styles.contentContainer}>
          <DefaultHeader
            selectedLevel={selectedLevel}
            gameConfig={gameConfig}
            onbackPress={() => {
              audioFunctionsRef.current.clearAudio();
              navigateBack();
            }}
          />

          <View style={styles.milestonesContainer}>
            {/* Path trail */}
            {completedTrail.length > 0 && (
              <View style={styles.pathTrailContainer}>
                {/* {completedTrail.map((point, index) => (
                    <View
                      key={index}
                      style={[
                        styles.trailPoint,
                        {
                          left: point.x - 2,
                          top: point.y - 2,
                        },
                      ]}
                    />
                  ))} */}
              </View>
            )}

            {gameState.loading ? (
              <LoadingUI isLoading={gameState.loading} />
            ) : apiStages.length <= 0 ? (
              <View style={styles.emptyStagesContainer}>
                <View style={styles.emptyStagesContent}>
                  <Text style={styles.emptyStagesTitle}>Chưa có chặng nào</Text>
                  <Text style={styles.emptyStagesText}>
                    Trình độ "{selectedLevel}" chưa có chặng nào được thiết lập.
                  </Text>
                </View>
              </View>
            ) : (
              milestones.map(m => {
                // Use dynamic scale calculation based on bottom position
                // percentageY là vị trí top, cọc gỗ số 1 có top cao (0.75) nên gần bottom
                const scaleFactor = calculateScale(m.percentageY || 0.5, milestones);
                const headerHeight = hp(6);

                // Use SVG path calculated positions directly
                const adjustedTop = headerHeight + (m.y || 0);
                const leftPosition = m.x || 0;

                // Kiểm tra xem có phải milestone cuối cùng và tất cả đã hoàn thành không
                const isLastMilestone = m.id === milestones.length;
                const allMilestonesCompleted = milestones.every(
                  milestone => milestone.status === 'completed',
                );
                const shouldShowBird =
                  m.status === 'in-progress' ||
                  (isLastMilestone && allMilestonesCompleted);

                // Debug log
                if (isLastMilestone) {
                  console.log('=== BIRD DEBUG - Last Milestone ===');
                  console.log('Milestone ID:', m.id);
                  console.log('Is last milestone:', isLastMilestone);
                  console.log(
                    'All milestones completed:',
                    allMilestonesCompleted,
                  );
                  console.log('Should show bird:', shouldShowBird);
                  console.log('Bird image exists:', !!gameConfig.birdImage);
                  console.log(
                    'Milestone statuses:',
                    milestones.map(ms => ({id: ms.id, status: ms.status})),
                  );
                }

                return (
                  <View key={m.id}>
                    {/* Bird animation for in-progress milestone or last milestone when all completed */}
                    {shouldShowBird && gameConfig.birdImage && (
                      <View
                        style={{
                          position: 'absolute',
                          top: adjustedTop - hp(9),
                          left: leftPosition - wp(5.5),
                          zIndex: 999,
                        }}>
                        <Image
                          source={gameConfig.birdImage}
                          style={{
                            width: wp(46) * scaleFactor,
                            height: wp(46) * scaleFactor,
                            aspectRatio: 1.7,
                            position: 'absolute',
                            // Điều chỉnh offset với hệ số mạnh hơn để con chim gần cọc gỗ hơn khi ở xa
                            // Sử dụng scaleFactor^2 để tăng hiệu ứng kéo gần
                            top: -wp(37) * Math.pow(scaleFactor, 1.5),
                            left: -wp(12) * Math.pow(scaleFactor, 1.5),
                          }}
                          resizeMode="contain"
                        />
                      </View>
                    )}
                    <View
                      style={[
                        styles.milestone,
                        {
                          top: adjustedTop,
                          left: leftPosition,
                          transform: [
                            {translateX: -wp(6)},
                            {translateY: -hp(7.5)},
                            {scale: scaleFactor},
                          ],
                        },
                      ]}>
                      <GenericMilestoneComponent
                        status={m.status}
                        number={m.id}
                        levelName={m.name}
                        onPress={() =>
                          handleMilestonePress(m.status, m.id, m.name)
                        }
                        scaleFactor={scaleFactor}
                        gameConfig={gameConfig}
                      />
                    </View>
                  </View>
                );
              })
            )}
          </View>
        </SafeAreaView>
        
      </ImageBackground>
<DefaultFooter
        onShowLevelModal={showLevelSelection}
        onShowHelp={showHelp}
        gameConfig={gameConfig}
        score={score}
      />
      

      {/* Start Game Modal */}
      {modalVisible && (
        <StartGameModal
          visible={modalVisible}
          onClose={closeModal}
          onStartGame={handleStartGame}
          competenceId={selectedCompetenceId}
          gameId={gameId}
          stageId={selectedMilestone?.number}
          content={gameConfig.modalContent}
          selectedMilestone={selectedMilestone}
        />
      )}

      {/* Choose Level Modal */}
      <ChooseLevelModal
        visible={showLevelModal}
        onClose={() => setShowLevelModal(false)}
        onSelectLevel={handleSelectLevel}
        unlockedLevels={unlockedLevels}
        selectedLevel={selectedLevel}
        gameId={gameId}
      />

      {/* Help Modal */}
      <HelpModal
        visible={showHelpModal}
        onClose={() => setShowHelpModal(false)}
        title="Hướng dẫn"
        content={gameConfig.helpText}
      />
    </View>
  );
};

// Default Header Component
const DefaultHeader = ({
  selectedLevel,
  gameConfig,
  onbackPress,
}: {
  selectedLevel: string;
  gameConfig: GameConfig;
  onbackPress: () => void;
}) => {
  const navigation = useNavigation<any>();

  return (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <TouchableOpacity
          style={{flexDirection: 'row', alignItems: 'center', zIndex: 10000}}
          onPress={onbackPress}>
          <Winicon src="outline/arrows/left-arrow" size={20} color="#fff" />
          <Text style={[styles.title, {color: gameConfig.colors.text}]}>
            {gameConfig.gameName}
          </Text>
        </TouchableOpacity>

        <View style={styles.levelDisplay}>
          <Image
            source={gameConfig.footerIcons.level}
            style={styles.levelIcon}
          />
          <Text style={styles.levelText}>{selectedLevel}</Text>
        </View>
      </View>
    </View>
  );
};

// Default Footer Component
const DefaultFooter = ({
  onShowLevelModal,
  onShowHelp,
  gameConfig,
  score,
}: {
  onShowLevelModal: () => void;
  onShowHelp: () => void;
  gameConfig: GameConfig;
  score: number;
}) => {
  const navigation = useNavigation<any>();

  const showRanking = () =>
    navigation.navigate('GameRanking', {gameId: gameConfig.gameId});

  return (
    <View style={[styles.footer, {backgroundColor: gameConfig.colors.footer}]}>
      <View style={styles.gem}>
        <Image source={gameConfig.footerIcons.coin} style={styles.gemIcon} />
        <Text style={styles.gemText}>{score}</Text>
      </View>

      <TouchableOpacity onPress={showRanking} style={styles.rankContainer}>
        <Image source={gameConfig.footerIcons.rank} style={styles.rankIcon} />
        <Text style={styles.footerText}>Xếp hạng</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onShowLevelModal} style={styles.rankContainer}>
        <Image source={gameConfig.footerIcons.level} style={styles.rankIcon} />
        <Text style={styles.footerText}>Trình độ</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onShowHelp} style={styles.rankContainer}>
        <Image source={gameConfig.footerIcons.help} style={styles.rankIcon} />
        <Text style={styles.footerText}>Hướng dẫn</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    position: 'relative'
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },
  milestonesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  milestoneContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  milestoneImage: {
    width: wp(18),
    height: hp(18),
    resizeMode: 'contain',
  },
  milestoneNumber: {
    fontSize: wp(6),
    fontWeight: 'bold',
    textAlign: 'center',
    position: 'absolute',
    zIndex: 15,
    top: Platform.OS === 'ios' ? '20%' : '15%',
    left: Platform.OS === 'ios' ? '47%' : '45%',
    transform: [{translateX: -wp(2)}, {translateY: -hp(0.5)}],
  },
  milestone: {
    position: 'absolute',
    width: wp(12),
    height: hp(15),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingTop: hp(1.5),
    paddingBottom: hp(1),
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'column',
  },
  title: {
    fontSize: normalize(18),
    fontWeight: 'bold',
    marginLeft: wp(3),
  },
  levelDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FCF8E8',
    borderRadius: wp(5),
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    marginTop: hp(2),
    marginLeft: wp(1),
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  levelIcon: {
    width: wp(5),
    height: wp(5),
    marginRight: wp(1.5),
    tintColor: '#FF6B35',
  },
  levelText: {
    fontSize: normalize(16),
    fontWeight: 'bold',
    color: '#333333',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: hp(1.5),
    width: '100%',
  },
  gem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: wp(2.5),
    paddingVertical: hp(0.6),
    borderRadius: wp(4),
  },
  gemIcon: {
    width: wp(6),
    height: wp(6),
    marginRight: wp(1.5),
  },
  gemText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: normalize(14),
  },
  footerText: {
    color: '#fff',
    fontWeight: 'bold',
    paddingHorizontal: wp(2.5),
    paddingVertical: hp(0.6),
    fontSize: normalize(14),
  },
  rankContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  rankIcon: {
    width: 20,
    height: 20,
  },
  emptyStagesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(8),
  },
  emptyStagesContent: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: wp(4),
    padding: wp(6),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  emptyStagesTitle: {
    fontSize: normalize(20),
    fontWeight: 'bold',
    color: '#112164',
    marginBottom: hp(1),
    textAlign: 'center',
  },
  emptyStagesText: {
    fontSize: normalize(16),
    color: '#333333',
    textAlign: 'center',
    marginBottom: hp(1),
    lineHeight: 22,
  },
  pathTrailContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  trailPoint: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#4CAF50',
    opacity: 0.7,
    shadowColor: '#4CAF50',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 3,
  },
});

export default GenericGameHomeScreen;
