/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import TitleWithBackAction from '../Layout/titleWithBackAction';
import {
  AppButton,
  closePopup,
  ComponentStatus,
  FDialog,
  FLoading,
  FPopup,
  showDialog,
  showPopup,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import WScreenFooter from '../Layout/footer';
import {randomGID, Ultis} from '../../utils/Utils';
import {TypoSkin} from '../../assets/skin/typography';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  View,
} from 'react-native';
import {TextFieldForm} from '../../modules/Default/form/component-form';
import {validatePhoneNumber} from '../../utils/validate';
import {DataController} from '../../base/baseController';
import {useForm} from 'react-hook-form';
import {StatusOrder} from '../../Config/Contanst';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {CourseDA} from '../../modules/Course/da';
import {navigateReset, RootScreen} from '../../router/router';
import {listenVnpayCallback} from '../../utils/VnpayService';
import {BaseDA} from '../../base/BaseDA';
import * as RNIap from 'react-native-iap';
// import { useLanguage } from '../../locales/languageContext';

const Order = () => {
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;

  const route = useRoute<any>();
  const {id, courseName} = route.params;
  const popupRef = useRef<any>(null);
  const [data, setData] = useState<any>();
  const methods = useForm({shouldFocusError: false});
  const [loading, setLoading] = useState(true);
  const [orderDetailId, setOrderDetailId] = useState<any>();
  const [courseCustomerId, setCourseCustomerId] = useState<any>();
  const dialogRef = useRef<any>(null);
  const courseDA = new CourseDA();
  // const eventEmitter = new NativeEventEmitter(VnpayMerchantModule);

  //   const {t} = useTranslation();
  // const { changeLanguage } = useLanguage();
  const getData = async () => {
    const result = await courseDA.getCourseDetail(id);
    if (result) {
      setData(result.data);
    }
    methods.setValue('Name', customer.Name);
    methods.setValue('Email', customer.Email);
    methods.setValue('Mobile', customer.Mobile);
    methods.setValue('Address', customer.Address);

    if (__DEV__) {
      methods.setValue('Name', 'Liem');
      methods.setValue('Mobile', '0972836794');
      methods.setValue('Address', 'Hà Nội');
    }
    setLoading(false);
  };

  useEffect(() => {
    getData();
  }, []);
  useEffect(() => {
    const purchaseUpdate = RNIap.purchaseUpdatedListener(async purchase => {
      const receipt = purchase.transactionReceipt;
      if (receipt) {
        // ✅ Ở đây bạn unlock content hoặc gửi receipt lên server để verify
        console.log('Thanh toán thành công:', purchase);
        console.log('Thanh toán thành công receipt:', receipt);
        const orderdetailcontroller = new DataController('OrderDetail');
        const coursecustomercontroller = new DataController('Course_Customer');
        const order = {
          Id: orderDetailId,
          Status: StatusOrder.success,
          Transaction: receipt,
        };
        const courseCustomer = {
          Id: courseCustomerId,
          Status: StatusOrder.success,
        };
        const result = await Promise.all([
          orderdetailcontroller.edit([order]),
          coursecustomercontroller.edit([courseCustomer]),
        ]);
        await RNIap.finishTransaction({purchase, isConsumable: false});
        if (result[0].code === 200 && result[1].code === 200) {
          _onPaySucess();
        }
      }
    });

    const purchaseError = RNIap.purchaseErrorListener(error => {
      console.warn('Lỗi thanh toán:', error);
    });

    return () => {
      purchaseUpdate.remove();
      purchaseError.remove();
    };
  }, []);

  const _onPaySucess = async () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.SUCCSESS,
      title: 'Thanh toán thành công',
      content:
        'Bây giờ bạn có thể tham gia khoá học này. Bạn muốn xem khoá học hay không?',
      onCancel: () => {
        navigateReset(RootScreen.navigateESchoolView);
      },
      titleCancel: 'Quay lại',
      titleSubmit: 'Xem khoá học',
      onSubmit: () => {
        navigateReset(RootScreen.navigateESchoolView, {
          screen: 'Learn',
        });
      },
    });
  };

  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const _onPay = async () => {
    var name = methods.watch('Name')?.trim();
    var mobile = methods.watch('Mobile')?.trim();
    var email = methods.watch('Email')?.trim();
    var address = methods.watch('Address')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (mobile) {
      if (!/^(\+84|0)/.test(mobile)) {
        mobile = '0' + mobile; // Add 0 at the beginning
      }
      const val = validatePhoneNumber(mobile);
      if (!val) {
        methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        return;
      }
    }
    setLoading(true);
    const order = {
      Id: randomGID(),
      Email: email,
      Name: name,
      DateCreated: new Date().getTime(),
      CustomerName: customer.Name,
      Phone: mobile,
      Address: address,
      Status: StatusOrder.proccess,
    };
    const orderDetail = {
      Id: randomGID(),
      Email: email,
      Name: courseName,
      DateCreated: new Date().getTime(),
      Mobile: mobile,
      Address: address,
      TotalPrice: data.Price,
      Price: data.Price,
      CustomerId: customer.Id,
      CourseId: id,
      OrderId: order.Id,
      Status:
        data.Price === 0 || data.Price === '0'
          ? StatusOrder.success
          : StatusOrder.proccess,
      VnpParams: 'null',
      Transaction: 'null',
    };
    const countLesson = await courseDA.getLessonbyCourseId(id);
    const courseCustomer = {
      Id: randomGID(),
      Email: email,
      Name: name,
      DateCreated: new Date().getTime(),
      Mobile: mobile,
      CourseId: id,
      CustomerId: customer.Id,
      IsCertificate: data.IsCertificate,
      Process: 0,
      OrderId: order.Id,
      Address: address,
      TotalLesson: countLesson?.length ?? 0,
      Status:
        data.Price === 0 || data.Price === '0'
          ? StatusOrder.success
          : StatusOrder.proccess,
    };
    const addOrder = new DataController('Order');
    const addOrderDetail = new DataController('OrderDetail');
    const addCourseCustomer = new DataController('Course_Customer');
    const response = await addOrder.add([order]);
    if (response.code === 200) {
      const responseDetail = await addOrderDetail.add([orderDetail]);
      if (responseDetail.code === 200) {
        setLoading(false);
        await addCourseCustomer.add([courseCustomer]);
        if (data.Price === 0 || data.Price === '0') {
          _onPaySucess();
          return;
        }
        //kiểm tra flatform
        const platform = Platform.OS;
        if (platform === 'ios') {
          // ios
          var obj = {
            sku: id,
          } as any;
          setCourseCustomerId(courseCustomer.Id);
          setOrderDetailId(orderDetail.Id);
          const getproduct = await RNIap.getProducts({skus: [id]});
          if(getproduct.length === 0){
            showSnackbar({
              message: 'Có lỗi xảy ra khi thanh toán',
              status: ComponentStatus.ERROR,
            });
            return;
          }
          console.log('getproduct', getproduct);
          await RNIap.requestPurchase(obj);
        } else {
          // android
          const url_payment = await BaseDA.createUrlPayment({
            Id: orderDetail.Id,
            Price: orderDetail.TotalPrice,
            Description: courseCustomer.Id,
            language: 'vn',
            orderType: 'course',
            os: 'mobile',
          });
          if (url_payment) {
            // openVnpay(url_payment.data);
            navigation.push(RootScreen.VnpayPaymentScreen, {
              paymentUrl: url_payment.data,
              id: id,
              OrderId: orderDetail.Id,
            });
          }
        }
      }
    }
    setLoading(false);
  };
  return (
    <TitleWithBackAction
      titleBottom={'Thông tin thanh toán'}
      onBack={() => {
        navigation.goBack();
      }}
      iconAction="outline/user interface/setup-tools">
      <FPopup ref={popupRef} />
      <FDialog ref={dialogRef} />
      {/* Main content */}
      <View style={{flex: 1, backgroundColor: ColorThemes.light.white}}>
        {/* <InforView methods={methods} /> */}
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            width: Dimensions.get('screen').width,
            flex: 1,
            paddingHorizontal: 16,
            marginTop: 16,
          }}>
          <FLoading
            visible={loading}
            avt={require('../../assets/appstore.png')}
          />
          <KeyboardAvoidingView
            behavior="padding"
            shouldRasterizeIOS
            style={{width: '100%', flex: 1}}>
            <View style={{width: '100%', gap: 16, flex: 1}}>
              <TextFieldForm
                control={methods.control}
                name="Name"
                required={true}
                placeholder="Họ và tên"
                // label="Họ và tên"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  padding: 16,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
                onBlur={(ev: string) => {
                  if (ev?.length !== 0) {
                    methods.clearErrors('Name');
                  } else {
                    methods.setError('Name', {
                      message: 'Họ và tên không được để trống',
                    });
                  }
                }}
              />
              <TextFieldForm
                control={methods.control}
                name="Email"
                placeholder="Email"
                // label="Họ và tên"
                returnKeyType="done"
                required={true}
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  padding: 16,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
                type="email-address"
                onBlur={(ev: string) => {
                  if (ev?.length !== 0) {
                    methods.clearErrors('Email');
                  } else {
                    methods.setError('Email', {
                      message: 'Email không được để trống',
                    });
                  }
                  const val = validateEmail(ev?.trim());
                  if (val) {
                    methods.clearErrors('Email');
                  } else {
                    methods.setError('Email', {
                      message: 'Email không hợp lệ',
                    });
                  }
                }}
              />
              <TextFieldForm
                control={methods.control}
                name="Mobile"
                required
                // label="Số điện thoại"
                placeholder="Nhập số điện thoại của bạn"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 46,
                      paddingHorizontal: 8,
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 8,
                      backgroundColor:
                        ColorThemes.light.Neutral_Background_Color_Main,
                      borderRadius: 8,
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText3,
                        color: ColorThemes.light.Neutral_Text_Color_Title,
                      }}>
                      +84
                    </Text>
                    <Winicon src="outline/arrows/down-arrow" size={16} />
                  </View>
                }
                type="number-pad"
                onBlur={async (ev: string) => {
                  if (ev) {
                    var mobile = ev?.trim();
                    // Check if the number doesn't already start with 0 or +84
                    if (!/^(\+84|0)/.test(mobile)) {
                      mobile = '0' + mobile; // Add 0 at the beginning
                    }
                    const val = validatePhoneNumber(mobile);
                    if (val) {
                      methods.clearErrors('Mobile');
                    } else {
                      methods.setError('Mobile', {
                        message: 'Số điện thoại không hợp lệ',
                      });
                    }
                  }
                }}
              />
              <TextFieldForm
                control={methods.control}
                name="Address"
                required={true}
                placeholder="Địa chỉ"
                // label="Họ và tên"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  padding: 16,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
              />
            </View>
          </KeyboardAvoidingView>
        </ScrollView>
        <WScreenFooter
          style={{
            flexDirection: 'row',
            gap: 8,
            paddingHorizontal: 16,
            paddingBottom: 32,
            alignItems: 'center',
          }}>
          <Text
            style={[
              {
                ...TypoSkin.heading6,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              },
            ]}>
            Tổng: {Ultis.money(data?.Price)} vnđ
          </Text>
          <View style={{flex: 1}} />
          <AppButton
            title={'Thanh toán'}
            backgroundColor={ColorThemes.light.Primary_Color_Main}
            borderColor="transparent"
            containerStyle={{
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 5,
            }}
            onPress={methods.handleSubmit(_onPay)}
            textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
        </WScreenFooter>
      </View>
    </TitleWithBackAction>
  );
};

export default Order;
