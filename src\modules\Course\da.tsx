import {DataController} from '../../base/baseController';
import {StatusOrder, StorageContanst} from '../../Config/Contanst';
import store from '../../redux/store/store';
import {getRandomElements, shuffleArray} from '../../utils/arrayUtils';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {randomGID} from '../../utils/Utils';
import {CustomerDA} from '../customer/da';
import {examDA} from '../exam/da';

export interface CourseItem {
  Id?: string;
  Name?: string;
  Img?: string;
  Price: number;
  IsCare: boolean;
  Description?: string;
  Status?: number;
}

export class CourseDA {
  private courseController: DataController;
  private orderDetailController: DataController;
  private lessonController: DataController;
  private wishlistCourseController: DataController;
  private courseCustomerController: DataController;
  private ratingController: DataController;
  private likeRatingController: DataController;
  private customerLessonController: DataController;
  private certificateController: DataController;
  private partController: DataController;

  constructor(
    courseController = new DataController('Course'),
    orderDetailController = new DataController('OrderDetail'),
    lessonController = new DataController('Lesson'),
    wishlistCourseController = new DataController('WishlistCourse'),
    courseCustomerController = new DataController('Course_Customer'),
    ratingController = new DataController('Rating'),
    likeRatingController = new DataController('Like_Rating'),
    customerLessonController = new DataController('CustomerLesson'),
    certificateController = new DataController('Customer_Certificate'),
    partController = new DataController('Part'),
  ) {
    this.courseController = courseController;
    this.orderDetailController = orderDetailController;
    this.lessonController = lessonController;
    this.wishlistCourseController = wishlistCourseController;
    this.courseCustomerController = courseCustomerController;
    this.ratingController = ratingController;
    this.likeRatingController = likeRatingController;
    this.customerLessonController = customerLessonController;
    this.certificateController = certificateController;
    this.partController = partController;
  }

  async getCoursePopular(pageSize: number) {
    // const responseCourse = await this.orderDetailController.group({
    //   searchRaw: '*',
    //   reducers: 'LOAD * GROUPBY 1 @CourseId REDUCE COUNT 0 AS CountCourse',
    // });

    var listProds: any[] = [];

    // if (responseCourse.code === 200) {
    //   var lstCourse = responseCourse.data;

    //   //sắp xếp lại khóa học sau khi đã count trong đơn hàng để lấy khóa đc mua nhiều nhất. và lấy top 10
    //   if (pageSize > 0) {
    //     lstCourse = [...lstCourse]
    //       .sort((a, b) => parseInt(a.CountCourse) - parseInt(b.CountCourse))
    //       .slice(0, pageSize);
    //   } else {
    //     lstCourse = [...lstCourse].sort(
    //       (a, b) => parseInt(a.CountCourse) - parseInt(b.CountCourse),
    //     );
    //   }

    //   const respone = await this.courseController.getListSimple({
    //     page: 1,
    //     size: 50,
    //     query: `@Id: {${lstCourse
    //       .map((item: any) => item.CourseId)
    //       .join(' | ')}} @Status: {true}`,
    //     returns: ['Id', 'Name', 'Price', 'Img'],
    //     sortby: { BY: 'DateCreated', DIRECTION: 'DESC' },
    //   });

    //   if (respone.code === 200) {
    //     listProds = [...respone.data];
    //   }
    // }

    // lấy khóa học hot
    const responeHot = await this.courseController.getListSimple({
      page: 1,
      size: 20,
      query: '@IsHot: {true} @Status: {true}',
      returns: ['Id', 'Name', 'Price', 'Img'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (responeHot?.data?.length > 0) {
      // random listProds and get pageSize items
      const randomList = shuffleArray(
        getRandomElements(responeHot.data, pageSize),
      );
      return randomList;
    }
    return [];
  }

  async getCourseRecent() {
    var listId = await getDataToAsyncStorage(StorageContanst.RecentCourse);
    if (listId != null) {
      const lst = listId.split(',');
      const respone = await this.courseController.getListSimple({
        query: `@Id: {${lst.join(' | ')}} @Status: {true}`,
        returns: ['Id', 'Name', 'Price'],
      });
      if (respone.code === 200) {
        return respone;
      }
    }
    return null;
  }

  async getAllList(
    page: number,
    size: number,
    query: string,
    returns?: Array<string>,
  ) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: query ?? '*',
      returns: returns,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyCategory(page?: any, size?: any, cateId?: string) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: `@CategoryId:{${cateId}} @Status: {true}`,
      returns: ['Id', 'Name', 'Description', 'Price', 'Img', 'CustomerId'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getAllListbyCustomerId(page: number, size: number, customerId: string) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: `@CustomerId:{${customerId}} @Status: {true}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyTopic(page: number, size: number, topicId: string) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: `@TopicId:{*${topicId}*} @Status: {true}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  // async getMycourse(status: number, customerId: string) {
  //   // lấy khóa học nằm trong đơn hàng đã hoàn thành
  //   const lst = await this.orderDetailController.getListSimple({
  //     page: 1,
  //     size: 1000,
  //     query: `@Status:[${StatusOrder.success}] @CustomerId: {${customerId}}`,
  //   });

  //   if (lst.code === 200) {
  //     var lstCourse = lst.data;
  //     lstCourse = [...lstCourse].map((item: any) => item.CourseId);

  //     const respone = await this.courseCustomerController.getListSimple({
  //       page: 1,
  //       size: 1000,
  //       query: `@CourseId:{${lstCourse.join(' | ')}}`,
  //     });

  //     if (respone.code === 200) {
  //       return respone;
  //     }
  //   }
  //   return null;
  // }

  async studentsCourse(id: string) {
    const respone = await this.orderDetailController.getPatternList({
      query: `@CourseId: {${id}} @Status: [${StatusOrder.success}]`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return 0;
  }

  async countStudentCourse(id: string) {
    const respone = await this.orderDetailController.getPatternList({
      query: `@CourseId: {${id}} @Status: [${StatusOrder.success}]`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (respone.code === 200) {
      return respone.data.length;
    }
    return 0;
  }

  async getCourseDetail(courseId: string) {
    const respone = await this.courseController.getById(courseId);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getLessonbyCourseId(courseId: string) {
    const respone = await this.partController.getPatternList({
      query: `@CourseId: {${courseId}}`,
      returns: [
        'Id',
        'Name',
        'Sort',
        'ExamId',
        'LessonId',
        'CourseId',
        'TestId',
      ],
      pattern: {
        CourseId: ['Id', 'Name', 'IsCertificate'],
        LessonId: ['Id', 'Name', 'Sort', 'Type', 'Video', 'Document', 'Hours'],
      },
    });
    if (respone.code === 200) {
      return respone?.Lesson;
    }
    return null;
  }
  async getPartbyCourseId(courseId: string) {
    const respone = await this.partController.getPatternList({
      query: `@CourseId: {${courseId}}`,
      returns: [
        'Id',
        'Name',
        'Sort',
        'ExamId',
        'LessonId',
        'CourseId',
        'TestId',
        'ExamScore',
        'Time'
      ],
      pattern: {
        CourseId: ['Id', 'Name', 'IsCertificate'],
        LessonId: [
          'Id',
          'Name',
          'Sort',
          'Type',
          'Video',
          'Document',
          'Introduction',
          'ExamId',
          'FlashCardId',
        ],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getLessonbyPartIds(partId: string) {
    const respone = await this.lessonController.getListSimple({
      page: 1,
      query: `@PartId: {${partId}}`,
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
      returns: [
        'Id',
        'Name',
        'Hours',
        'Introduction',
        'Video',
        'Document',
        'Sort',
      ],
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getLessonInCourseDetail(courseId: string, check?: boolean) {
    const response = await this.getPartbyCourseId(courseId);
    if (response && response.data && response.data.length > 0) {
      const parts = response.data;
      parts.sort((a: any, b: any) => a.Sort - b.Sort);
      if (parts.length === 0) {
        return null;
      }
      const partIds = response.data.map((part: any) => part.Id);
      const allLessonsInParts = response.Lesson;
      const proccess = await this.getProccessInPart(courseId, partIds);
      const updateParts = parts.map((part: any) => {
        const lessonIds = part.LessonId?.split(',').filter((id: any) => id);
        const lessons =  allLessonsInParts
            .filter((lesson: any) => lessonIds?.includes(lesson?.Id))
            .map((lesson: any) => {
              const lessonProgress = proccess?.find((p: any) =>
                p.LessonId?.includes(lesson?.Id),
              );
              return {
                ...lesson,
                isCompleted: lessonProgress ? true : false,
                icon: 'color/development/book-open',
              };
            });
        return {
          ...part,
          Lesson: lessons,
          isCompleted: lessons.every((lesson: any) => lesson.isCompleted),
        };
      });
      return updateParts;
    }
    return null;
  }

  async getLessonDetail(lessonId: string) {
    const respone = await this.lessonController.getById(lessonId);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getWishlistCourse(page: number, size: number) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const respone = await this.wishlistCourseController.getListSimple({
      query: `@CustomerId: {${cusId}}`,
    });

    if (respone.code === 200) {
      var lstId = [...respone.data].map(item => item.CourseId);

      const courserespone = await this.courseController.getListSimple({
        page: page,
        size: size,
        query: `@Id: {${lstId.join(' | ')}}`,
      });

      if (courserespone.code === 200) {
        return courserespone;
      }
    }
    return null;
  }

  async checkCourseIsWishlishCustomer(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.wishlistCourseController.getListSimple({
        query: `@CustomerId: {${cusId}} @CourseId:{${id}}`,
        size: 1,
        returns: ['Id'],
      });
      if (respone.data?.length > 0) {
        return true;
      }
    }
    return false;
  }

  async deleteWishlistCourse(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const course = await this.wishlistCourseController.getListSimple({
      query: `@CourseId: {${id}} @CustomerId: {${cusId}}`,
    });

    if (course?.data?.length > 0) {
      const respone = await this.wishlistCourseController.delete([
        course.data[0].Id,
      ]);
      if (respone.code === 200) {
        return respone;
      }
    }
    return null;
  }

  async addWishlistCourse(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const data = {
      Id: randomGID(),
      CustomerId: cusId,
      CourseId: id,
      DateCreated: new Date().getTime(),
    };

    const respone = await this.wishlistCourseController.add([data]);
    if (respone.code === 200) {
      return data;
    }
    return null;
  }

  async getRatingCourse(id: string) {
    const courserespone = await this.ratingController.getListSimple({
      query: `@CourseId: {${id}}`,
    });
    if (courserespone.code === 200) {
      return courserespone;
    }
    return null;
  }

  async getLikesRatingCourse(id: string) {
    const courserespone = await this.likeRatingController.getListSimple({
      query: `@RatingId: {${id}}`,
    });
    if (courserespone.code === 200) {
      return courserespone.data?.length ?? 0;
    }
    return 0;
  }

  async addRating(data: any) {
    const courserespone = await this.ratingController.add([data]);
    if (courserespone.code === 200) {
      return data;
    }
    return null;
  }

  async addLikeRating(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const data = {
      Id: randomGID(),
      RatingId: id,
      CustomerId: cusId,
    };
    const courserespone = await this.likeRatingController.add([data]);
    if (courserespone.code === 200) {
      return data;
    }
    return false;
  }

  async addCommentRating(id: string, content: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const data = {
      Id: randomGID(),
      RatingId: id,
      CustomerId: cusId,
      Message: content,
      DateCreated: new Date().getTime(),
    };
    const courserespone = await this.ratingController.add([data]);
    if (courserespone.code === 200) {
      return data;
    }
    return null;
  }
  async getMyCourse() {
    var cusId = store.getState().customer.data?.Id;
    if (cusId) {
      const respone = await this.courseCustomerController.getPatternList({
        query: `@CustomerId: {${cusId}} @Status: [${StatusOrder.success}]`,
        pattern: {
          CourseId: ['Id', 'Name', 'CustomerId', 'Img', 'IsCertificate', 'CertificateId'],
        },
      });
      if (respone.code === 200) {
        //proccess
        const courseIds = respone.data?.map((item: any) => item.CourseId);
        //lấy total lesson
        const courseDA = new CourseDA();
        const proccess = await this.getProccessLessonInCourse(courseIds);
        try {
        const updateData = await Promise.all(respone.data?.map( async (item: any)  => {
          const countLesson = await courseDA.getLessonbyCourseId(item.CourseId);
          const tmp = proccess?.filter((p: any) => p.CourseId === item.CourseId);
          debugger
          tmp?.map((p: any) => {
            p.LessonId = p.LessonId?.split(',') ?? [];
          });
          const Course = respone.Course.find((a: any) => a.Id === item.CourseId);
          return {
            ...item,
            Name: Course?.Name,
            IsCertificate: Course?.IsCertificate,
            Img: Course?.Img,
            certificateName: Course?.Name,
            Process: tmp?.reduce((acc: any, cur: any) => {
              return acc + (cur.LessonId?.filter((id: any) => id !== '' && id !== null && id !== undefined && id !== 'null' && id !== 'undefined')?.length ?? 0);
            }, 0),
            TotalLesson: countLesson?.length ?? 0,
            Description: `${tmp?.reduce((acc: any, cur: any) => {
              return acc + (cur.LessonId?.filter((id: any) => id !== '' && id !== null && id !== undefined && id !== 'null' && id !== 'undefined')?.length ?? 0);
            }, 0)}/${
                  item.TotalLesson ?? 0
                } Bài giảng`,
          };
        }));
        
        return updateData;
        } catch (error) {
          console.error('Error processing my course item:', error);
        }
      }
    }
    return null;
  }  
  async getProccessLessonInCourse(courseIds: string[]) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseIds.join(' | ')}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async getProccessLesson(courseId: string, lessonId: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @LessonId: {${lessonId}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async getProccessVideoLesson(courseId: string, lessonId: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @LessonId: {${lessonId}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async getProccessInPart(courseId: string, partIds: string[]) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @PartId: {${partIds.join(
          ' | ',
        )}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async checkExistsProccessLesson(courseId: string, partId: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @CustomerId:{${cusId}} @PartId: {${partId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data[0];
      }
    }
    return null;
  }

  async addProccessLesson(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.add([data]);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  async updateProccessLesson(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.edit([data]);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  async updateCourseCustomer(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.courseCustomerController.edit([data]);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  async getCourseCustomerItem(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.courseCustomerController.getById(id);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  //certi
  async getListCertificate() {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.getListSimple({
        query: `@CustomerId: {${cusId}}`,
      });
      if (respone?.code === 200) {
        return respone;
      }
    }
    return null;
  }
  async getCertificateItembyId(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.getById(id);
      if (respone?.code === 200) {
        return respone;
      }
    }
    return null;
  }
  async getCertificateItem(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.getListSimple({
        query: `@CustomerId: {${cusId}} @CourseId: {${id}}`,
      });
      if (respone?.code === 200 && respone?.data?.length > 0) {
        return respone?.data[0];
      }
    }
    return null;
  }
  async addCertificate(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.add([data]);
      if (respone?.code === 200) {
        return respone;
      }
    }
    return null;
  }
  //end certi
}
