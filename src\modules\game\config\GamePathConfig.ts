// Dynamic Game Path Configuration System

export interface MilestonePosition {
  id: number;
  top: number;
  left: number;
  levelName?: string;
}

export interface GamePathConfig {
  gameId: string;
  milestonePositions: MilestonePosition[];
  pathStyle: 'curved' | 'zigzag' | 'spiral' | 'linear' | 'wave';
  pathConfig?: {
    curvature?: number; // 0-1, độ cong của đường
    smoothness?: number; // Độ mượt
    direction?: 'clockwise' | 'counterclockwise';
    amplitude?: number; // Cho wave path
  };
  // Custom SVG path nếu cần
  customPathData?: string;
}

// Game-specific path configurations
export const GAME_PATH_CONFIGS: Record<string, GamePathConfig> = {
  'cf86bc33ef03447fa744eea2bbf31cfc': { // game ALTP
    gameId: 'cf86bc33ef03447fa744eea2bbf31cfc',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.75, left: 0.5, levelName: '<PERSON><PERSON> bản'},
      {id: 2, top: 0.68, left: 0.7, levelName: '<PERSON><PERSON>'},
      {id: 3, top: 0.59, left: 0.55, levelName: 'Trung bình'},
      {id: 4, top: 0.57, left: 0.37, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.4, levelName: 'Chuyên gia'},
      {id: 7, top: 0.44, left: 0.5, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },

  '7eb45b0edc6247c3bde6bbb15547dfda': { // game Saku TB
    gameId: '7eb45b0edc6247c3bde6bbb15547dfda',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.73, left: 0.35, levelName: 'Cơ bản'},
      {id: 2, top: 0.68, left: 0.58, levelName: 'Dễ'},
      {id: 3, top: 0.59, left: 0.75, levelName: 'Trung bình'},
      {id: 4, top: 0.53, left: 0.65, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.46, left: 0.45, levelName: 'Chuyên gia'},
      {id: 7, top: 0.42, left: 0.35, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },

  '1d56852db9964d9a878a1d9d5f872cb7': { // game DHBC
    gameId: '1d56852db9964d9a878a1d9d5f872cb7',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.74, left: 0.35, levelName: 'Cơ bản'},
      {id: 2, top: 0.68, left: 0.55, levelName: 'Dễ'},
      {id: 3, top: 0.60, left: 0.30, levelName: 'Trung bình'},
      {id: 4, top: 0.55, left: 0.20, levelName: 'Khó'},
      {id: 5, top: 0.52, left: 0.33, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.4, levelName: 'Chuyên gia'},
      {id: 7, top: 0.45, left: 0.3, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },

  // Thêm config cho các game khác
  '769bce29753d4fa48314325c4bc7ebb0': { // game Saku LC
    gameId: '769bce29753d4fa48314325c4bc7ebb0',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.72, left: 0.27, levelName: 'Cơ bản'},
      {id: 2, top: 0.67, left: 0.50, levelName: 'Dễ'},
      {id: 3, top: 0.59, left: 0.67, levelName: 'Trung bình'},
      {id: 4, top: 0.52, left: 0.60, levelName: 'Khó'},
      {id: 5, top: 0.48, left: 0.50, levelName: 'Rất khó'},
      {id: 6, top: 0.46, left: 0.35, levelName: 'Chuyên gia'},
      {id: 7, top: 0.42, left: 0.40, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },
  '1b1804be1c6049c2876d1626794fa7a0': { // game MGHH
    gameId: '1b1804be1c6049c2876d1626794fa7a0',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.72, left: 0.68, levelName: 'Cơ bản'},
      {id: 2, top: 0.65, left: 0.45, levelName: 'Dễ'},
      {id: 3, top: 0.59, left: 0.55, levelName: 'Trung bình'},
      {id: 4, top: 0.55, left: 0.67, levelName: 'Khó'},
      {id: 5, top: 0.51, left: 0.52, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.42, levelName: 'Chuyên gia'},
      {id: 7, top: 0.45, left: 0.63, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },
  '19d8c9d61ae74f968416b28fcf8e93c3': { // game Saku Xayto
    gameId: '19d8c9d61ae74f968416b28fcf8e93c3',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.73, left: 0.72, levelName: 'Cơ bản'},
      {id: 2, top: 0.68, left: 0.45, levelName: 'Dễ'},
      {id: 3, top: 0.59, left: 0.65, levelName: 'Trung bình'},
      {id: 4, top: 0.55, left: 0.30, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.70, levelName: 'Chuyên gia'},
      {id: 7, top: 0.44, left: 0.60, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },
  '05ac80f4e3b54615afb06d49f5140ade': { // game Saku San moi
    gameId: '05ac80f4e3b54615afb06d49f5140ade',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.73, left: 0.35, levelName: 'Cơ bản'},
      {id: 2, top: 0.68, left: 0.65, levelName: 'Dễ'},
      {id: 3, top: 0.57, left: 0.48, levelName: 'Trung bình'},
      {id: 4, top: 0.53, left: 0.35, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.49, left: 0.65, levelName: 'Chuyên gia'},
      {id: 7, top: 0.475, left: 0.78, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },
  'e856cf006f2745bda1b21dba65df4d71': { // game VTNV
    gameId: 'e856cf006f2745bda1b21dba65df4d71',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.72, left: 0.40, levelName: 'Cơ bản'},
      {id: 2, top: 0.67, left: 0.68, levelName: 'Dễ'},
      {id: 3, top: 0.57, left: 0.75, levelName: 'Trung bình'},
      {id: 4, top: 0.52, left: 0.67, levelName: 'Khó'},
      {id: 5, top: 0.48, left: 0.59, levelName: 'Rất khó'},
      {id: 6, top: 0.45, left: 0.52, levelName: 'Chuyên gia'},
      {id: 7, top: 0.42, left: 0.55, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },
  'c0b7fbb981bf495295de84d6b6b008c7': { // game VTNV
    gameId: 'c0b7fbb981bf495295de84d6b6b008c7',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.72, left: 0.5, levelName: 'Cơ bản'},
      {id: 2, top: 0.65, left: 0.67, levelName: 'Dễ'},
      {id: 3, top: 0.60, left: 0.35, levelName: 'Trung bình'},
      {id: 4, top: 0.56, left: 0.57, levelName: 'Khó'},
      {id: 5, top: 0.52, left: 0.50, levelName: 'Rất khó'},
      {id: 6, top: 0.49, left: 0.42, levelName: 'Chuyên gia'},
      {id: 7, top: 0.44, left: 0.5, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },






};

// Path generators for different styles
export class GamePathGenerator {
  static generateCurvedPath(positions: MilestonePosition[], config?: any): string {
    if (positions.length === 0) return "";

    const sortedPositions = [...positions].sort((a, b) => a.id - b.id);
    let pathData = "";
    const curvature = config?.curvature || 0.6;

    sortedPositions.forEach((position, index) => {
      if (index === 0) {
        pathData += `M ${position.left} ${position.top}`;
      } else if (index === 1) {
        const prev = sortedPositions[index - 1];
        const midX = (prev.left + position.left) / 2;
        const midY = (prev.top + position.top) / 2;

        const offsetX = (position.left - prev.left) * curvature * 0.3;
        const offsetY = (position.top - prev.top) * curvature * 0.2;

        const controlX = midX + offsetX;
        const controlY = midY - Math.abs(offsetY);

        pathData += ` Q ${controlX} ${controlY} ${position.left} ${position.top}`;
      } else {
        const prev = sortedPositions[index - 1];
        const next = sortedPositions[index + 1];

        const deltaX = position.left - prev.left;
        const deltaY = position.top - prev.top;

        const cp1X = prev.left + deltaX * 0.4 * curvature;
        const cp1Y = prev.top + deltaY * 0.2 * curvature;

        let cp2X, cp2Y;
        if (next) {
          const nextDeltaX = next.left - position.left;
          const nextDeltaY = next.top - position.top;
          cp2X = position.left - nextDeltaX * 0.3 * curvature;
          cp2Y = position.top - nextDeltaY * 0.2 * curvature;
        } else {
          cp2X = position.left - deltaX * 0.3 * curvature;
          cp2Y = position.top - deltaY * 0.1 * curvature;
        }

        pathData += ` C ${cp1X} ${cp1Y} ${cp2X} ${cp2Y} ${position.left} ${position.top}`;
      }
    });

    return pathData;
  }

  static generateZigzagPath(positions: MilestonePosition[]): string {
    if (positions.length === 0) return "";

    const sortedPositions = [...positions].sort((a, b) => a.id - b.id);
    let pathData = `M ${sortedPositions[0].left} ${sortedPositions[0].top}`;

    for (let i = 1; i < sortedPositions.length; i++) {
      pathData += ` L ${sortedPositions[i].left} ${sortedPositions[i].top}`;
    }

    return pathData;
  }

  static generateSpiralPath(positions: MilestonePosition[], config?: any): string {
    if (positions.length === 0) return "";

    const direction = config?.direction || 'clockwise';
    const curvature = config?.curvature || 0.8;

    // Implementation for spiral path
    return this.generateCurvedPath(positions, { curvature });
  }

  static generateWavePath(positions: MilestonePosition[], config?: any): string {
    if (positions.length === 0) return "";

    const sortedPositions = [...positions].sort((a, b) => a.id - b.id);
    const amplitude = config?.amplitude || 0.3;
    const curvature = config?.curvature || 0.5;

    let pathData = `M ${sortedPositions[0].left} ${sortedPositions[0].top}`;

    for (let i = 1; i < sortedPositions.length; i++) {
      const prev = sortedPositions[i - 1];
      const current = sortedPositions[i];

      // Create wave-like curves between points
      const midX = (prev.left + current.left) / 2;
      const midY = (prev.top + current.top) / 2;

      // Add wave oscillation
      const waveOffset = Math.sin(i * Math.PI) * amplitude * curvature;
      const controlX = midX + waveOffset;
      const controlY = midY;

      pathData += ` Q ${controlX} ${controlY} ${current.left} ${current.top}`;
    }

    return pathData;
  }
}

// Utility to get game path config
export const getGamePathConfig = (gameId: string): GamePathConfig => {
  const config = GAME_PATH_CONFIGS[gameId];
  if (!config) {
    throw new Error(`Game path config not found for gameId: ${gameId}`);
  }
  return config;
};

// Generate path data for a game
export const generateGamePath = (gameId: string): string => {
  const config = getGamePathConfig(gameId);

  if (config.customPathData) {
    return config.customPathData;
  }

  switch (config.pathStyle) {
    case 'curved':
      return GamePathGenerator.generateCurvedPath(config.milestonePositions, config.pathConfig);
    case 'zigzag':
      return GamePathGenerator.generateZigzagPath(config.milestonePositions);
    case 'spiral':
      return GamePathGenerator.generateSpiralPath(config.milestonePositions, config.pathConfig);
    case 'wave':
      return GamePathGenerator.generateWavePath(config.milestonePositions, config.pathConfig);
    case 'linear':
      return GamePathGenerator.generateZigzagPath(config.milestonePositions);
    default:
      return GamePathGenerator.generateCurvedPath(config.milestonePositions, config.pathConfig);
  }
};
