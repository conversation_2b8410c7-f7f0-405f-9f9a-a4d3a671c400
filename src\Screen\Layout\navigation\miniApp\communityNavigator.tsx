import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootScreen} from '../../../../router/router';
import CommunityLayout from '../../../../modules/community/pages/layout';
import AllGroupsLoadMore from '../../../../modules/community/groups/listview/allGroupsLoadMore';
import GroupIndex from '../../../../modules/community/groups';
import PostDetail from '../../../../modules/community/detail/postDetail';
import CreatePost from '../../../../modules/community/pages/createPost';
import NotifCommunity from '../../../../modules/notification/view/inCommunity';
import Profile from '../../../../modules/customer/profile';
import ProfileCommunity from '../../../../modules/community/pages/profileIndex';
import NewsDetail from '../../../../modules/community/detail/newDetail';

const CommunityNavigation: React.FC = () => {
  const Stack = createNativeStackNavigator();
  return (
    <Stack.Navigator
      screenOptions={{headerShown: false}}
      initialRouteName={RootScreen.CommunityLayout}
    >
      <Stack.Screen
        name={RootScreen.CommunityLayout}
        component={CommunityLayout}
      />
      {/* groups */}
      <Stack.Screen
        name={RootScreen.GroupIndex}
        component={GroupIndex}
        // options={{
        //   animation: 'slide_from_bottom',
        //   animationDuration: 250,
        // }}
      />
      <Stack.Screen
        name={RootScreen.AllGroupsLoadMore}
        component={AllGroupsLoadMore}
      />
      <Stack.Screen name={RootScreen.PostDetail} component={PostDetail} />
      <Stack.Screen name={RootScreen.NewsDetail} component={NewsDetail} />
      <Stack.Screen
        name={RootScreen.ProfileCommunity}
        component={ProfileCommunity}
      />    
      <Stack.Screen name={RootScreen.NotifCommunity} component={NotifCommunity} />

      <Stack.Screen
        name={RootScreen.createPost}
        component={CreatePost}
        options={{
          animation: 'slide_from_bottom',
          animationDuration: 250,
        }}
      />
    </Stack.Navigator>
  );
};

export default CommunityNavigation;
