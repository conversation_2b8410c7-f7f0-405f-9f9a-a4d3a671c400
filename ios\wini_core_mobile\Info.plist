<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>ITM E-School Pro</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.632449728851-i72fbju5hl0jhkan6ncbv3uplk7ej736</string>
				<string>com.googleusercontent.apps.632449728851-7ffpkm5q12qaqk5psvr31jblfvgknjvt</string>
				<string>com.googleusercontent.apps.352157365911-atbspo0ubnpaks1i5i8kcf3bd8ki7fub</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-632449728851-ios-ddc7155d388a09483fa1a6</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>vnpayITM</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>vnpayITM</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSAllowsArbitraryLoadsForMedia</key>
		<true/>
		 <key>NSExceptionDomains</key>
    	<dict>
        <key>cdneschool.itm.vn</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <true/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.0</string>
        </dict>
    </dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app requires to access your photo library to show image on profile and send via chat</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>This app needs access to Documents folder to save downloaded videos for offline viewing</string>
	<key>NSDownloadsFolderUsageDescription</key>
	<string>This app needs access to Downloads folder to save downloaded videos for offline viewing</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Authentication with Face ID</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location is required for find the pitch around you on app</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location is required for find the pitch around you on app</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires to access your microphone to record video with your voice send via chat</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires to access your photo library to show image on profile and send via chat</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>Allow Notification to receive about important news in app</string>
	<key>UIAppFonts</key>
	<array>
		<string>NotoSansJP-VariableFont_wght.ttf</string>
		<string>NotoSansJP-Black.ttf</string>
		<string>NotoSansJP-Bold.ttf</string>
		<string>NotoSansJP-ExtraBold.ttf</string>
		<string>NotoSansJP-ExtraLight.ttf</string>
		<string>NotoSansJP-Light.ttf</string>
		<string>NotoSansJP-Medium.ttf</string>
		<string>NotoSansJP-Regular.ttf</string>
		<string>NotoSansJP-SemiBold.ttf</string>
		<string>NotoSansJP-Thin.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>SKAdNetworkItems</key>
	<array/>
	<key>NSUserTrackingUsageDescription</key>
	<string>This identifier will be used to deliver personalized ads to you.</string>
</dict>
</plist>
