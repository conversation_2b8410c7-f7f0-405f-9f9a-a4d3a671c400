import {DataController} from '../../base/baseController';
import store from '../../redux/store/store';
import {randomGID} from '../../utils/Utils';

export class GameDA {
  private gameController: DataController;
  private historyGameController: DataController;
  private CompetenceController: DataController;
  private gamecustomerController: DataController;
  private gamecustomerCompetenceController: DataController;
  private gameStageController: DataController;

  constructor() {
    this.gameController = new DataController('Game');
    this.historyGameController = new DataController('HistoryScore');
    this.CompetenceController = new DataController('GameCompetence');
    this.gamecustomerController = new DataController('GameCustomer');
    this.gamecustomerCompetenceController = new DataController(
      'GameCustomerCompetence',
    );
    this.gameStageController = new DataController('GameStage');
  }

  async getGameList() {
    const respone = await this.gameController.getListSimple({
      query: '@IsActive: {true}',
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  /**
   * Lấy top người chơi có điểm cao nhất từ bảng HistoryGame
   * @param limit Số lượng người chơi muốn lấy
   * @param gameId ID của game (nếu muốn lọc theo game cụ thể)
   * @returns Danh sách top người chơi
   */
  async getTopPlayers(limit: number = 10, type: number = 1, gameId?: string) {
    try {
      // Tạo query để lấy dữ liệu
      let query = '';

      // Nếu có gameId, thêm điều kiện lọc theo gameId
      if (gameId) {
        query = `@GameId: {${gameId}}`;
      }
      if(type === 2){
        query += ` @Type: [${type}]`;
      }else{
        query += ` -@Type: [2]`;
      }
      // Lấy dữ liệu từ bảng HistoryGame
      const response = await this.historyGameController.getPatternList({
        query: query || '*',
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
        },
        size: 100, // Lấy nhiều bản ghi để xử lý gộp theo người dùng
      });
      debugger
      if (response.code === 200 && response.data.length > 0) {
        // Gộp điểm theo người dùng
        const playerScores = new Map();

        response.data.forEach((record: any) => {
          const customerId = record.CustomerId;
          const score = record.Score || 0;
          const name = record.Name || 'Unknown';
          const avatarUrl =
            response.Customer.find((a: any) => a.Id === customerId)
              ?.AvatarUrl || '';

          if (playerScores.has(customerId)) {
            // Nếu người dùng đã có trong map, cộng thêm điểm
            const existingData = playerScores.get(customerId);
            playerScores.set(customerId, {
              ...existingData,
              score: existingData.score + score,
            });
          } else {
            // Nếu người dùng chưa có trong map, thêm mới
            playerScores.set(customerId, {
              customerId,
              name,
              score,
              avatarUrl,
            });
          }
        });

        // Chuyển map thành mảng và sắp xếp theo điểm giảm dần
        const sortedPlayers = Array.from(playerScores.values())
          .sort((a, b) => b.score - a.score)
          .slice(0, limit);

        return sortedPlayers;
      }

      return [];
    } catch (error) {
      console.error('Error getting top players:', error);
      return [];
    }
  }
  async getTopPlayersSakupi(limit: number = 10) {
    try {
      // Tạo query để lấy dữ liệu
      let query = '';
      // Lấy dữ liệu từ bảng HistoryGame
      const response = await this.historyGameController.getPatternList({
        query: query || '*',
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
        },
        // size: 100, // Lấy nhiều bản ghi để xử lý gộp theo người dùng
      });
      debugger
      if (response.code === 200 && response.data.length > 0) {
        // Gộp điểm theo người dùng
        const playerScores = new Map();

        response.data.forEach((record: any) => {
          const customerId = record.CustomerId;
          const score = record.Score || 0;
          const name = record.Name || 'Unknown';
          const avatarUrl =
            response.Customer.find((a: any) => a.Id === customerId)
              ?.AvatarUrl || '';

          if (playerScores.has(customerId)) {
            // Nếu người dùng đã có trong map, cộng thêm điểm
            const existingData = playerScores.get(customerId);
            playerScores.set(customerId, {
              ...existingData,
              score: existingData.score + score,
            });
          } else {
            // Nếu người dùng chưa có trong map, thêm mới
            playerScores.set(customerId, {
              customerId,
              name,
              score,
              avatarUrl,
            });
          }
        });
          debugger
        // Chuyển map thành mảng và sắp xếp theo điểm giảm dần
        const sortedPlayers = Array.from(playerScores.values())
          .sort((a, b) => b.score - a.score)
          .slice(0, limit);

        return sortedPlayers;
      }

      return [];
    } catch (error) {
      console.error('Error getting top players:', error);
      return [];
    }
  }
  async getTopPlayersbyGameId(limit: number = 10, gameId?: string) {
    try {
      // Tạo query để lấy dữ liệu
      let query = '*';

      // Nếu có gameId, thêm điều kiện lọc theo gameId
      if (gameId) {
        query = `@GameId: {${gameId}}`;
      }

      // Lấy dữ liệu từ bảng HistoryGame
      const response = await this.gamecustomerController.getPatternList({
        query: query,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
        },
        size: 100, // Lấy nhiều bản ghi để xử lý gộp theo người dùng
      });
      if (response.code === 200 && response.data.length > 0) {
        // Gộp điểm theo người dùng
        const playerScores = new Map();

        response.data.forEach((record: any) => {
          const customerId = record.CustomerId;
          const score = record.Score || 0;
          const name = response.Customer.find((a: any) => a.Id === customerId)
              ?.Name  || 'Unknown';
          const avatarUrl =
            response.Customer.find((a: any) => a.Id === customerId)
              ?.AvatarUrl || '';

          if (playerScores.has(customerId)) {
            // Nếu người dùng đã có trong map, cộng thêm điểm
            const existingData = playerScores.get(customerId);
            playerScores.set(customerId, {
              ...existingData,
              score: existingData.score + score,
            });
          } else {
            // Nếu người dùng chưa có trong map, thêm mới
            playerScores.set(customerId, {
              customerId,
              name,
              score,
              avatarUrl,
            });
          }
        });

        // Chuyển map thành mảng và sắp xếp theo điểm giảm dần
        const sortedPlayers = Array.from(playerScores.values())
          .sort((a, b) => b.score - a.score)
          .slice(0, limit);

        return sortedPlayers;
      }

      return [];
    } catch (error) {
      console.error('Error getting top players:', error);
      return [];
    }
  }
  //lấy danh sách trình độ theo gameId
  async getCompetencebyGameIdAndCustomerId(gameId: string, customerId: string) {
    const respone = await this.gamecustomerCompetenceController.getListSimple({
      query: `@GameId: {${gameId}} @CustomerId: {${customerId}}`,
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  // lưu trình độ của người chơi
  async addCompetence(data: any) {
    const respone = await this.gamecustomerCompetenceController.add([data]);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async editCompetence(data: any) {
    const respone = await this.gamecustomerCompetenceController.edit([data]);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  // Lấy danh sách stages theo GameCompetenceId
  async getStagesByCompetenceId(competenceId: string, gameId: string) {
    const response = await this.gameStageController.getListSimple({
      query: `@GameCompetenceId: {${competenceId}} @GameId: {${gameId}}`,
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (response.code === 200) {
      return response;
    }
    return null;
  }
  // lấy điểm của người chơi theo game
  async getScoreByCustomerIdAndGameId(customerId: string, gameId: string) {
    const response = await this.gamecustomerController.getListSimple({
      query: `@CustomerId: {${customerId}} @GameId: {${gameId}}`,
    });
    debugger
    if (response.code === 200) {
      return response.data.reduce(
        (acc: number, cur: any) => acc + parseInt(cur.Score ?? 0),
        0,
      );
    }
    return null;
  }

  async insertScore({
    milestoneId,
    gameId,
    competenceId,
    status,
    score,
    name,
  }: {
    milestoneId: number;
    gameId: string;
    competenceId: string;
    status: number;
    score: number;
    name: string;
  }) {
    try {
      // let id: string;
      // Nếu chưa có, tạo mới
      const customerId = store.getState().customer.data.Id;
      const game = {
        Id: randomGID(),
        CustomerId: customerId,
        GameId: gameId,
        Stage: milestoneId,
        Competency: competenceId,
        Status: status,
        DateCreated: new Date().getTime(),
        Score: score,
        HighestScore: score,
        PlayedAt: new Date().getTime(),
        Name: name,
      };
      debugger
      console.log('Tạo bản ghi mới:', game);
      const result = await this.gamecustomerController.add([game]);
      if (result.code !== 200) {
        return false;
      }
      return true;
    } catch (error) {
      console.error('Lỗi khi thêm điểm vào bảng HistoryScore:', error);
    }
  }
}
