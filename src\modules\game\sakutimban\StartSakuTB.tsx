import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet, Text, ActivityIndicator, BackHandler} from 'react-native';
import {useDispatch} from 'react-redux';
import {useSelector} from 'react-redux';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import ListAnswer from './components/ListAnswer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {SafeAreaView} from 'react-native-safe-area-context';
import GameOverModal from '../components/GameOverModel';
import {BottomGame} from '../components/BottomGame';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuTBHook} from '../../../redux/hook/game/sakuTBHook';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import ModelPauseGame from '../components/ModelPauseGame';
import sakuTBDA from './da/sakuTBDA';
import { useGameAudio } from '../ailatrieuphu/hooks/useGameAudio';

const StartSakuTB = () => {
  const dispatch: AppDispatch = useDispatch();
  const {isGameOver, messageGameOver} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {questionDone, totalQuestion, maxLives, currentLives, timeLimit} =
    useSelector((state: RootState) => state.SakuTB);
  const gameHook = useGameHook();
  const sakuTbHook = useSakuTBHook();
  //navigate
  const navigation = useNavigation<any>();
  //router param
  const route = useRoute<any>();
  const {competenceId, milestoneId} = route.params || {competenceId: '1'};

  // State cho Winner Modal
  const [isPause, setIsPause] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
const {
    audioState,
    playCorrectAnswer,
    playInCorrectAnswer,
    playWin,
    playGameOver,
    playTimeWarning,
    stopCurrentSound,
    stopTimeWarning,
    clearAllSounds,
    toggleMute,
  } = useGameAudio();
  useFocusEffect(
    useCallback(() => {
      console.log('StartDHBC focused');

      return () => {
        console.log('StartDHBC blurred - clearing all sounds');
        clearAllSounds();
      };
    }, [clearAllSounds]),
  );
  // Handle hardware back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        console.log('DHBC: Hardware back button pressed');
        clearAllSounds();
        return false; // Let default behavior handle navigation
      },
    );
    return () => backHandler.remove();
  }, [clearAllSounds]);
  const initData = async () => {
    try {
      setLoading(true);
      sakuTbHook.resetState();

      // Load game config first
      const gameConfig = await sakuTBDA.loadGameConfig(ConfigAPI.gameSakuTB);
      sakuTbHook.applyGameConfig(gameConfig);
      debugger
      const data = await sakuTBDA.loadGameQuestions({
        gameId: ConfigAPI.gameSakuTB,
        stage: milestoneId,
        competenceId: competenceId,
      });
      sakuTbHook.setData('questions', data.transformedQuestions);
      await gameHook.getCurrentScore(ConfigAPI.gameSakuTB);
      sakuTbHook.startGame();
      sakuTbHook.setData('competenceId', competenceId);
    } catch (error: any) {
      console.error('Error loading game data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load game config and questions on mount
  useEffect(() => {
    initData();
  }, [dispatch, competenceId, milestoneId]);

  useEffect(() => {
    if (currentLives < 1) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);
  useEffect(() => {
    if (isGameOver) {
      playGameOver();
    }
  }, [isGameOver]);
  // tạm dừng game
  const onPauseGame = useCallback(() => {
    gameHook.pauseGame();
    setIsPause(true);
  }, [gameHook]);

  // tiếp tục game
  const onContinueGame = useCallback(() => {
    gameHook.continueGame();
    setIsPause(false);
  }, [gameHook]);

  const restartGame = () => {
    gameHook.setData({stateName: 'isGameOver', value: false});
    sakuTbHook.startGame();
    // Sync global timer với SakuTB config
    if (timeLimit) {
      console.log(
        `[StartSakuTB] Syncing global timer with SakuTB config: ${timeLimit}s`,
      );
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});

      // Sync lives với SakuTB config
      if (maxLives) {
        sakuTbHook.setData('currentLives', maxLives);
      }
    } else {
      // Fallback nếu config chưa load
      gameHook.resetGame();
    }
  };

  const gameOver = (message: string) => {
    playGameOver();
    gameHook.gameOver(message);
  };


  // Show loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color="#112164" />
          <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (error) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>Lỗi tải dữ liệu: {error}</Text>
          <Text style={styles.errorSubText}>
            Đang sử dụng dữ liệu dự phòng...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.mainContainer}>
        <View style={styles.container}>
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            gameId={ConfigAPI.gameSakuTB}
          />
          <LineProgressBar
            progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
          <View style={styles.headerInfoContainer}>
            <Lives totalLives={maxLives} currentLives={currentLives}></Lives>
            <CountBadge
              current={questionDone + 1}
              total={totalQuestion}></CountBadge>
          </View>

          {/* Danh sách các đáp án */}
          <View style={styles.listAnswerContainer}>
            <ListAnswer milestoneId={milestoneId}></ListAnswer>
          </View>

          {/* Bottom game */}
          <View style={styles.bottomGameContainer}>
            <BottomGame
              resetGame={restartGame}
              backGame={() => {
                navigation.goBack();
              }}
              pauseGame={onPauseGame}
              volumeGame={() => {}}></BottomGame>
          </View>
        </View>
      </View>
      {/* Model Thất bại */}
      <GameOverModal
        visible={isGameOver}
        onClose={() => {}}
        restartGame={restartGame}
        message={messageGameOver}
        isTimeOut={false}
      />
      <ModelPauseGame
        visible={isPause}
        onContinue={onContinueGame}
        message={'Bạn đang tạm dừng trò chơi'}
      />
      {/* Winner Modal */}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    position: 'relative',
    marginHorizontal: 16,
    marginTop: 12,
    alignItems: 'center',
  },
  mainContainer: {
    backgroundColor: '#F1D1A6',
    alignItems: 'center',
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#112164',
    marginTop: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#FF5722',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  errorSubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  questionInfo: {
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    width: '100%',
    alignItems: 'center',
  },
  questionNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#112164',
    marginBottom: 4,
  },
  questionContent: {
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
    lineHeight: 22,
  },
  headerInfoContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  listAnswerContainer: {
    marginTop: 16,
  },
  bottomGameContainer: {
    position: 'absolute',
    bottom: 6,
  },
});

export default StartSakuTB;
