import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {flashCardDA} from '../../flashcard/da';
import Sound from 'react-native-sound';
import {useNavigation} from '@react-navigation/native';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import {ScrollView} from 'react-native-gesture-handler';
import TitleWithBackAction from '../../../Screen/Layout/titleWithBackAction';
import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import {View} from 'react-native';
import {SkeletonImage, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import TitleWithBottom from '../../../Screen/Layout/titleWithBottom';
import FastImage from '@d11/react-native-fast-image';

export const FlashCardCourse = (pros: any) => {
  const [listDetail, setListDetail] = useState<any[]>();
  const [cardItem, setcardItem] = useState<any>();
  const [flashCardList, setFlashCardList] = useState<any[]>([]);
  const [activeFlashCardId, setActiveFlashCardId] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingId, setCurrentPlayingId] = useState<string | null>(null);
  const da = useMemo(() => new flashCardDA(), []);
  const soundRef = useRef<Sound | null>(null);

  // Hàm phát audio
  const playAudio = (audioUrl: string, cardId: string) => {
    // Stop any currently playing sound
    stopAudio();

    // Set up sound
    Sound.setCategory('Playback');

    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      
      if (error) {
        console.log('Failed to load sound', error);
        setIsPlaying(false);
        setCurrentPlayingId(null);
        return;
      }

      // Play the sound
      setIsPlaying(true);
      setCurrentPlayingId(cardId);
      sound.play(success => {
        if (success) {
          console.log('Sound played successfully');
        } else {
          console.log('Sound playback failed');
        }
        setIsPlaying(false);
        setCurrentPlayingId(null);
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Hàm dừng audio
  const stopAudio = () => {
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
      setIsPlaying(false);
      setCurrentPlayingId(null);
    }
  };

  // Hàm load detail cho một flashcard cụ thể
  const loadFlashCardDetail = useCallback(async (flashCardId: string) => {
    const flashcardDetail = await da.getListDetailbyId(flashCardId);

    if (flashcardDetail) {
      const list = await Promise.all(
        flashcardDetail.data.map(async (card: any) => {
          const isLearned = await da.checkCardIsLearned(card.Id);
          return {
            ...card,
            isLearned,
          };
        }),
      );
      setListDetail(list);
    }
  }, [da]);

  // Hàm map dữ liệu
  const mapingData = async () => {
    if (!pros.lessonData?.FlashCardId) {
      return;
    }
    
    // Xử lý multiple flashcard IDs
    const flashcardIds = pros.lessonData?.FlashCardId?.split(',')
      .map((id: string) => id.trim())
      .filter((id: string) => id && id !== 'null' && id !== 'undefined' && id !== '0');

    if (flashcardIds.length === 0) {
      return;
    }

    // Lấy thông tin các flashcard
    const flashcard = await da.getbyIdPattern(flashcardIds.join(' | '));
    if (flashcard && flashcard.data.length > 0) {
      //sort 
      flashcard.data.sort((a: any, b: any) => {
        return (a.Sort || 0) - (b.Sort || 0);
      });
      setFlashCardList(flashcard.data);
      setActiveFlashCardId(flashcard.data[0].Id);
      setcardItem(flashcard.data[0]);

      // Load detail cho flashcard đầu tiên
      await loadFlashCardDetail(flashcard.data[0].Id);
    }
  };

  // Hàm xử lý khi click vào tag flashcard
  const handleFlashCardTagPress = useCallback(async (flashCard: any) => {
    setActiveFlashCardId(flashCard.Id);
    setcardItem(flashCard);
    await loadFlashCardDetail(flashCard.Id);
  }, [activeFlashCardId]);

  useEffect(() => {
    
    mapingData();
  }, [pros.lessonData]);
  const navigation = useNavigation<any>();
  useEffect(() => {
    // Lấy danh sách thẻ đã học từ
    const unsubscribe = navigation.addListener('focus', () => {
      mapingData();
    });
    return unsubscribe;
  }, [navigation]);

  return (
    <ScrollView style={{padding: 16, flex: 1}}>
      {!cardItem?.Id ? null : (
        <>
        {/* Hiển thị tags flashcard nếu có nhiều hơn 1 */}
        {flashCardList.length > 1 && (
          <View style={styles.flashCardTagsContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.flashCardTagsScrollView}
            >
              {flashCardList.map((flashCard: any) => (
                <TouchableOpacity
                  key={flashCard.Id}
                  style={[
                    styles.flashCardTag,
                    activeFlashCardId === flashCard.Id && styles.flashCardTagActive,
                  ]}
                  onPress={() => handleFlashCardTagPress(flashCard)}
                >
                  <Text style={[
                    styles.flashCardTagText,
                    activeFlashCardId === flashCard.Id && styles.flashCardTagTextActive,
                  ]}>
                    {flashCard.Name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        <Text style={styles.headerTitle}>{cardItem?.Name}</Text>
      <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
        <Winicon
          src="outline/files/document-copy"
          size={12}
          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
        />
        <Text
          style={{
            ...TypoSkin.subtitle4,
            color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            marginRight: 8,
          }}>
          {`${listDetail?.length ?? 0} từ`}
        </Text>
        <Winicon
          src="outline/user interface/view"
          size={12}
          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
        />
        <Text
          style={{
            ...TypoSkin.subtitle4,
            color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            marginRight: 8,
          }}>
          {`${cardItem?.Count ?? 0} người học`}
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            paddingHorizontal: 8,
            borderRadius: 24,
            borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
            borderWidth: 1,
            paddingVertical: 4,
            gap: 4,
          }}>
          <Text
            style={{
              fontSize: 12,
              color: '#61616B',
            }}>
            {cardItem?.Topic}
          </Text>
        </View>
      </View>
      <View style={styles.learningModes}>
        <Text style={styles.sectionTitle}>Bắt đầu học</Text>
        <View style={styles.modeButtonsContainer}>
          <TouchableOpacity
            style={styles.modeButton}
            onPress={() => {
              navigate(RootScreen.FlashcardMode, {list: listDetail});
            }}>
            <Text style={styles.modeButtonText}>Flashcard</Text>
          </TouchableOpacity>
        </View>
      </View>

      <Text style={styles.sectionTitle}>Danh sách thẻ</Text>
      {listDetail?.map((card: any, index: number) => {
        const isLearned = card.isLearned;
        const isCurrentlyPlaying = currentPlayingId === card.Id && isPlaying;
        return (
          <View key={card.Id} style={styles.enhancedCardItem}>
            {/* Header với ảnh, tiêu đề và actions */}
            <View style={styles.cardHeader}>
              {/* Ảnh minh họa */}
              <View style={styles.imageContainer}>
                {card.Img ? (
                  <FastImage
                    source={{
                      uri: card.Img.includes('http')
                        ? card.Img
                        : ConfigAPI.getValidLink(card.Img),
                    }}
                    style={styles.cardImage}
                  />
                ) : (
                  <View style={styles.placeholderImage}>
                    <Winicon
                      src="outline/files/image"
                      size={20}
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                    />
                  </View>
                )}
              </View>

              {/* Nội dung chính */}
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>
                  {index + 1}. {card.Name}
                </Text>
                <Text style={styles.cardDefinition} numberOfLines={2}>
                  {card.Define}
                </Text>
                {card.Example && (
                  <Text style={styles.cardExample} numberOfLines={1}>
                    Ví dụ: {card.Example}
                  </Text>
                )}
              </View>

              {/* Action buttons */}
              <View style={styles.actionButtons}>
                {/* Learned status indicator - chỉ hiển thị */}
                <View
                  style={[
                    styles.learnedIndicator,
                    isLearned && styles.learnedIndicatorActive,
                  ]}>
                  {isLearned && (
                    <Text
                      style={{
                        ...TypoSkin.subtitle4,
                        color: ColorThemes.light.Success_Color_Main,
                      }}>
                      Đã học
                    </Text>
                  )}
                </View>

                {/* Audio button */}
                {card.Audio && (
                  <TouchableOpacity
                    style={[styles.audioButton]}
                    onPress={() => {
                      if (isCurrentlyPlaying) {
                        stopAudio();
                      } else {
                        const audioUrl = ConfigAPI.getValidLink(card.Audio);
                        playAudio(audioUrl, card.Id);
                      }
                    }}>
                    <Winicon
                      src={
                        isCurrentlyPlaying
                          ? 'color/multimedia/button-pause'
                          : 'fill/multimedia/sound'
                      }
                      size={16}
                      color="#61616B"
                    />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
        );
      })}
      <View style={{height: 50}} />
        </>
      ) }
      
    </ScrollView>
  );
};

// Styles
const styles = StyleSheet.create({
  progressBar: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginTop: 24,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4C7BF4',
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 24,
    transform: [{perspective: 1000}], // Cần thiết cho hiệu ứng 3D
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  cardTouchable: {
    width: '100%',
    height: '100%',
  },
  flashcard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 24,
    height: '100%',
    width: '100%',
    // shadowColor: '#000',
    // shadowOffset: {width: 0, height: 2},
    // shadowOpacity: 0.2,
    // shadowRadius: 4,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    backfaceVisibility: 'hidden', // Quan trọng cho hiệu ứng lật
  },
  cardFace: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
  },
  flashcardText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  flashcardText1: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  flipHintContainer: {
    position: 'absolute',
    bottom: 16,
  },
  flipHint: {
    color: '#999',
    fontSize: 12,
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  bottomControlsContainer: {
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  subTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  deckItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  deckTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  deckCount: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  button: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: 'bold',
    fontSize: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 12,
    color: '#333',
  },
  cardItem: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 8,
    marginBottom: 16,
  },
  enhancedCardItem: {
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  imageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  cardContent: {
    flex: 1,
    gap: 4,
  },
  cardTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: '600',
  },
  cardDefinition: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    lineHeight: 20,
  },
  cardExample: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 8,
    alignItems: 'center',
  },
  audioButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,

    // borderColor: ColorThemes.light.Primary_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  audioButtonActive: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  learnedIndicator: {
    width: 75,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  learnedIndicatorActive: {
    // cho background rgb nhạt hơn chút.
    backgroundColor: ColorThemes.light.Success_Color_Background,
    borderRadius: 14,
  },
  cardFront: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },

  learningModes: {
    marginVertical: 16,
  },
  modeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modeButton: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  modeButtonText: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: 'bold',
  },

  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginBottom: 16,
    paddingHorizontal: 40,
  },
  navButton: {
    backgroundColor: '#e0e0e0',
    padding: 12,
    borderRadius: 8,
    width: '48%',
    alignItems: 'center',
  },
  navButtonText: {
    color: '#333',
    fontWeight: 'bold',
  },
  arrowButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    alignItems: 'center',
    justifyContent: 'center',

    borderWidth: 1,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  prevButton: {
    // Có thể thêm style riêng cho nút trước nếu cần
  },
  nextButton: {
    // Có thể thêm style riêng cho nút tiếp nếu cần
  },
  disabledArrowButton: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderColor: ColorThemes.light.Neutral_Text_Color_Subtitle,
    shadowOpacity: 0.05,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  learnCardQuestion: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  learnCardAnswer: {
    fontSize: 18,
    color: '#4C7BF4',
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold',
  },
  showAnswerButton: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 24,
  },
  showAnswerButtonText: {
    color: '#4C7BF4',
    fontWeight: 'bold',
  },
  feedbackButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  feedbackButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  incorrectButton: {
    backgroundColor: '#ff6b6b',
  },
  correctButton: {
    backgroundColor: '#4ecdc4',
  },
  feedbackButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  resultText: {
    fontSize: 18,
    textAlign: 'center',
    marginVertical: 24,
    color: '#333',
  },
  // New styles for enhanced flashcard
  eyeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  frontCardName: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  frontIconsContainer: {
    position: 'absolute',
    top: 85,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    zIndex: 10,
  },
  frontAudioButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  frontEyeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  frontImageContainer: {
    width: 300,
    height: 300,
    borderRadius: 12,
    overflow: 'hidden',
    // marginBottom: 20,
    alignSelf: 'center',
  },
  frontCardImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    // marginTop: 40,
  },
  frontImagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  transcriptionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    position: 'absolute',
    bottom: 60,
  },
  transcriptionLeft: {
    flex: 1,
    alignItems: 'flex-start',
  },
  transcriptionRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  transcriptionText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    textAlign: 'center',
    lineHeight: 24,
  },
  backCardName: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 16,
  },
  backAudioButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  backContentContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingHorizontal: 20,
    marginTop: 8,
  },
  backDefinitionContainer: {
    marginBottom: 16,
  },
  backDefinitionLabel: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 8,
    fontWeight: '600',
  },
  backDefinitionText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    lineHeight: 24,
    textAlign: 'left',
  },
  backExampleContainer: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  backExampleText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    fontStyle: 'italic',
    lineHeight: 22,
    textAlign: 'left',
  },
  // Styles cho flashcard tags
  flashCardTagsContainer: {
    marginBottom: 16,
  },
  flashCardTagsScrollView: {
    flexGrow: 0,
  },
  flashCardTag: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  flashCardTagActive: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  flashCardTagText: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    fontWeight: '500',
  },
  flashCardTagTextActive: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: '600',
  },
});
